version: 2.1

# Define workflows
workflows:
  deploy-services:
    jobs:
      - deploy:
          filters:
            branches:
              only: /^deployment\/.*-service-dev$/

jobs:
  deploy:
    docker:
      - image: cimg/base:stable

    steps:
      - checkout

      - run:
          name: Extract service and environment
          command: |
            BRANCH_NAME=${CIRCLE_BRANCH}
            SERVICE=$(echo $BRANCH_NAME | sed -E 's/deployment\/([a-z]+)-service-.*/\1/')
            echo "export SERVICE=$SERVICE" >> $BASH_ENV
            echo "Deploying $SERVICE service to dev environment"

      - run:
          name: Setup Google Cloud SDK
          command: |
            # Install Google Cloud SDK
            echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | sudo tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
            curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -
            sudo apt-get update && sudo apt-get install google-cloud-cli

      - run:
          name: Authenticate with Google Cloud
          command: |
            echo $GOOGLE_APPLICATION_CREDENTIALS | base64 --decode > ${HOME}/gcp-key.json
            gcloud auth activate-service-account --key-file ${HOME}/gcp-key.json
            gcloud config set project friendly-idea-384714
            gcloud config set compute/zone asia-southeast1-a

      - run:
          name: Configure Google Cloud SDK
          command: |
            gcloud auth configure-docker asia-southeast1-docker.pkg.dev
            sudo apt-get install google-cloud-cli-gke-gcloud-auth-plugin
            gcloud container clusters get-credentials cluster-nexdor --zone asia-southeast1-a --project friendly-idea-384714

      - setup_remote_docker:
          version: 20.10.24

      - run:
          name: Install kubectl
          command: |
            sudo apt-get update && sudo apt-get install -y kubectl

      - run:
          name: Build, push and apply docker image
          command: |
            source $BASH_ENV
            make deploy-${SERVICE}-image-dev
