apiVersion: apps/v1
kind: Deployment
metadata:
  name: cron-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cron-service
  template:
    metadata:
      labels:
        app: cron-service
    spec:
      containers:
        - name: cron-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/cron-service:250306123305
          envFrom:
            - configMapRef:
                name: nexpos-env
