apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  labels:
    app: notification-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: notification-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/notification-service:241127091846
          command: ["./notification-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-env
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: notification-service
  labels:
    app: notification-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: notification-service
---
apiVersion: v1
kind: Service
metadata:
  name: notification-lb
  labels:
    app: notification-service
spec:
  selector:
    app: notification-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer