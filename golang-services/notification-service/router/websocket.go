package router

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"
)

type Client struct {
	conn        *websocket.Conn
	query       map[string]string
	accessToken string
}

type Message struct {
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

type ClientMessage struct {
	AccessToken string `json:"access_token"`
	Topic       string `json:"topic"`
	Type        string `json:"type"`
}

var (
	clients         = make(map[*websocket.Conn]*Client)
	broadcasterChan = make(chan Message)
	upgrader        = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}
)

func HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Error upgrading to WebSocket: %v", err)
		return
	}
	defer conn.Close()

	_, message, err := conn.ReadMessage()
	if err != nil {
		log.Printf("Error reading initial WebSocket message: %v", err)
		return
	}
	fmt.Println("Initial message:", string(message))

	var clientMsg ClientMessage
	if err := json.Unmarshal(message, &clientMsg); err != nil {
		log.Printf("Error unmarshaling client message: %v", err)
		return
	}

	if !isValidateAccessToken(clientMsg.AccessToken) {
		log.Printf("Invalid access token")
		conn.WriteJSON(map[string]string{
			"error": "Invalid access token",
		})
		return
	}

	query := map[string]string{
		"topic": clientMsg.Topic,
	}
	client := &Client{
		conn:        conn,
		query:       query,
		accessToken: clientMsg.AccessToken,
	}
	clients[conn] = client
	defer delete(clients, conn)

	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			log.Printf("Error reading WebSocket message: %v", err)
			break
		}

		var clientMsg ClientMessage
		if err := json.Unmarshal(message, &clientMsg); err != nil {
			log.Printf("Error unmarshaling message: %v", err)
			continue
		}

		if clientMsg.Type == "ping" {
			err = conn.WriteJSON(map[string]string{
				"type": "pong",
			})
			if err != nil {
				log.Printf("Error sending pong: %v", err)
				break
			}
			continue
		}

		if clientMsg.Topic != "" {
			client.query["topic"] = clientMsg.Topic
		}
	}
}

func Broadcaster() {
	for {
		message := <-broadcasterChan
		fmt.Println("Broadcasting:", string(message.Data))

		messageTopic := gjson.ParseBytes(message.Data).Get("topic").String()

		for _, client := range clients {
			if !isValidateAccessToken(client.accessToken) {
				client.conn.Close()
				delete(clients, client.conn)
				continue
			}

			shouldSend := false
			if client.query["topic"] == "" && messageTopic == "notifications" {
				shouldSend = true
			} else if strings.Contains(client.query["topic"], messageTopic) {
				shouldSend = true
			}

			if shouldSend {
				err := client.conn.WriteJSON(message)
				if err != nil {
					log.Printf("Error broadcasting message: %v", err)
					client.conn.Close()
					delete(clients, client.conn)
				}
			}
		}
	}
}

func RedisListener(rdb *redis.Client) {
	ctx := context.Background()
	pubsub := rdb.Subscribe(ctx, "saas_notifications", "print_order", "print_bill")
	defer pubsub.Close()

	for {
		msg, err := pubsub.ReceiveMessage(ctx)
		if err != nil {
			log.Printf("Error receiving message from Redis: %v", err)
			continue
		}

		fmt.Println("Redis message:", msg.Payload)

		var message Message
		err = json.Unmarshal([]byte(msg.Payload), &message)
		if err != nil {
			log.Printf("Error unmarshaling message: %v", err)
			continue
		}

		if isMatchingNotification(message) {
			broadcasterChan <- message

			err := rdb.Del(ctx, msg.Payload).Err()
			if err != nil {
				log.Printf("Error deleting record from Redis: %v", err)
			}
		}
	}
}

func isMatchingNotification(message Message) bool {
	return true
}

func isValidateAccessToken(token string) bool {
	return true
	// return token != ""
}
