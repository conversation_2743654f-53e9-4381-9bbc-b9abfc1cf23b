package router

import (
	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/order-service/router/handlers"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
)

func LoadHandlers(r gin.IRouter) gin.IRouter {
	required := middlewares.RequireAuth
	// internalRequired := middlewares.RequireAuth("internal")
	// authByAPIKey := middlewares.AuthByAPIKey

	api := r.Group("v1/order-service")
	{
		api.GET("health", HealthCheck)
		// File Management
		api.POST("upload/images", required(), UploadImage)
		api.POST("upload/multiple-images", required(), UploadMultipleImages)
		api.POST("upload/files", required(), UploadFile)
		api.POST("file/headers", required(), GetFileHeaders)

		posGroup := api.Group("pos")
		{
			posGroup.POST("get_vouchers", required(), GetAvailableVouchers)
			posGroup.POST("get_shipments", required(), GetShipment)
			posGroup.POST("get_pickup_slots", required(), GetPickupSlots)
			posGroup.POST("apply_vouchers", required(), ApplyVouchers)
			posGroup.POST("orders", required(), CreatePosOrder)
		}

		WebOrderGroup := api.Group("web")
		{
			WebOrderGroup.POST("get_vouchers", required(), GetAvailableVouchers)
			WebOrderGroup.POST("get_shipments", required(), GetShipment)
			WebOrderGroup.POST("get_pickup_slots", required(), GetPickupSlots)
			WebOrderGroup.POST("apply_vouchers", required(), ApplyVouchers)
			WebOrderGroup.POST("orders", required(), CreateWebOrder)
			WebOrderGroup.GET("payment_services", required(), GetPaymentServices)
		}

		// Brand Retailer Management
		// General brand retailer endpoints
		brandsRetailerGroup := api.Group("brands/retailer")
		{
			// Get all sale configurations (requires permission)
			brandsRetailerGroup.GET("sale-config", required("sale_config"), GetSaleConfig)
		}

		// Brand-specific retailer endpoints
		brandRetailerGroup := api.Group("brands/:brand_id/retailer")
		{
			// Get sale config by brand
			brandRetailerGroup.GET("sale-config", GetSaleConfigByBrand)

			// Create new sale config
			brandRetailerGroup.POST("sale-config", required("add_promotion"), BrandCreateConfig)

			// Update existing sale config
			brandRetailerGroup.PUT("sale-config/:config_id", required("update_promotion"), BrandUpdateConfig)

			// Delete sale config
			brandRetailerGroup.DELETE("sale-config/:config_id", required("delete_promotion"), BrandDeleteSaleConfig)

			// Get voucher usage statistics
			brandRetailerGroup.GET("sale-config/:config_id/voucher-usage", required(), GetVoucherUsage)
		}

		hubsGroup := api.Group("hubs/:hub_id")
		{
			// Stock Management
			stockGroup := hubsGroup.Group("stock")
			{
				// stockGroup.POST("menu/get_stock_from_servers", required("hub_stock"), GetMenuStockFromServer)
				// stockGroup.GET("items", required("hub_stock"), GetStockItems)
				// stockGroup.POST("items", required("hub_stock"), CreateStockItem)
				// stockGroup.PUT("items/:item_id", required("hub_stock"), UpdateStockItem)
				// stockGroup.DELETE("items/:code", required("hub_stock"), DeleteStockItem)
				// stockGroup.GET("file/templates", required("hub_stock"), GetStockFileTemplate)
				// stockGroup.GET("files", required("hub_stock"), GetStockFiles)
				// stockGroup.POST("files", required("hub_stock"), CreateStockFile)
				// stockGroup.GET("items/:code/histories", required("hub_stock"), GetStockHistories)
				// stockGroup.GET("summary", required("hub_stock"), GetStockSummary)

				// Stock Tickets
				stockGroup.GET("tickets", required("hub_stock"), GetHubStockTicketList)
				stockGroup.POST("tickets", required("hub_stock"), CreateHubStockTicket)
				stockGroup.PUT("tickets/:ticket_id", required("hub_stock"), UpdateHubStockTicket)
				stockGroup.POST("tickets/:ticket_id/approve", required("hub_stock"), ApproveHubStockTicket)
				stockGroup.POST("tickets/:ticket_id/reject", required("hub_stock"), RejectHubStockTicket)
				stockGroup.DELETE("tickets/:ticket_id", required("hub_stock"), DeleteHubStockTicket)
				stockGroup.POST("tickets/:ticket_id/print", required("hub_stock"), PrintHubStockTicket)
				stockGroup.POST("tickets/:ticket_id/download", required("hub_stock"), PrintHubStockTicket)
			}
		}

		sitesGroup := api.Group("sites/:site_id")
		{
			// Cart Management
			sitesGroup.GET("user/cart", required(), GetCart)
			sitesGroup.POST("user/cart", required(), UpdateCart)
			cartGroup := sitesGroup.Group("user/cart")
			{
				cartGroup.POST("items", required(), UpdateCartItem)
				cartGroup.POST("clone", required(), CloneCartFromOrder)
				cartGroup.GET("gifts", required(), GetCartGifts)
				cartGroup.POST("addresses", required(), UpdateCartAddress)
				cartGroup.GET("shipments", required(), GetCartShipment)
				cartGroup.POST("shipments", required(), UpdateCartShipment)
				// cartGroup.GET("pickup_slots", required(), GetPickupSlots)
				// cartGroup.POST("shipping_promo", required(), ApplyPromoCode)
				cartGroup.POST("apply_voucher", required(), ApplyVoucher)
				cartGroup.POST("check_voucher", required(), CheckVoucher)
				// cartGroup.POST("create", required(), CreateOrder)
				cartGroup.POST("reset", required(), ResetCart)
			}

			// Order Payment Endpoints
			sitesGroup.GET("user/orders/:order_id/payment_urls", required(), GetOrderPaymentURLs)
			sitesGroup.GET("user/orders/:order_id/re_payments", required(), RePaymentForOrder)
		}

		// Order Management
		api.GET("site/orders", required(), GetMultiSiteOrderList)
		api.GET("site/order/counts", required(), GetMultiSiteOrderListCount)
		api.GET("site/new_orders", required(), GetMultiSiteNewOrders)
		orderGroup := api.Group("site/orders/:order_id")
		{
			orderGroup.GET("", required(), GetOrderDetail)
			orderGroup.PUT("", required(), UpdateOrder)
			orderGroup.DELETE("", required(), DeleteOrder)
			// orderGroup.GET("latest", required(), GetOrderDetailLatest)
			orderGroup.POST("confirm", required(), ConfirmOrder)
			orderGroup.POST("cancel", required(), CancelOrder)
			orderGroup.POST("confirm_payments", required(), ConfirmPayment)
			orderGroup.POST("delete_payments", required(), DeletePayment)
			orderGroup.GET("prints", required(), PrintOrder)
			orderGroup.GET("print_payment_bill", required(), PrintOrderPaymentBill)

			// Shipments
			orderGroup.GET("shipments", required(), GetOrderShipments)
			orderGroup.POST("shipments", required(), CreateOrderShipments)
			orderGroup.POST("manual_shipments", required(), CreateOrderManualShipments)
			orderGroup.POST("shipment/cancel", required(), CancelOrderShipments)

			// Transactions
			orderGroup.GET("transactions/:transaction_id", required(), GetOrderTransaction)
		}

		// Public Order Endpoints
		api.GET("public/site/orders/:order_id/payment_urls", GetPublicOrderPaymentURLs)
		api.GET("share/payments", ShareOrderPaymentURL)

		// // Partner Management
		// partnerGroup := api.Group("partner")
		// {
		// 	partnerGroup.POST("deactivate", required("partner"), DeactivateSelf)
		// 	partnerGroup.GET("saved-accounts", required("partner"), GetSavedBankAccounts)
		// 	partnerGroup.GET("customers", required("partner"), GetCustomers)
		// 	partnerGroup.POST("customers", required("partner"), CreateCustomer)
		// 	partnerGroup.PUT("customers/:customer_id", required("partner"), UpdateCustomer)
		// 	partnerGroup.GET("commission", required("partner"), GetPartnerCommissions)
		// 	partnerGroup.GET("report", required("partner"), GetPartnerReport)
		// 	partnerGroup.GET("top-sales", required("partner"), GetTopSaleItems)
		// 	partnerGroup.GET("withdraws", required("partner"), GetPartnerWithdraws)
		// 	partnerGroup.GET("banners", required("partner"), GetPartnerBanners)
		// }

		// Cron Jobs
		internalRequired := middlewares.RequireAuth("internal")
		api.GET("test/notifications", handlers.TestOrderNotificationsHandler)
		cronGroup := api.Group("cron")
		{
			// cronGroup.GET("token_account/refresh", internalRequired, CronRefreshTokenAccounts)
			// cronGroup.GET("orders", internalRequired, CronSiteOrders)
			// cronGroup.GET("ecom/orders", internalRequired, CronSiteEcomOrders)
			// cronGroup.GET("site/finances", CronSiteFinances)
			// cronGroup.GET("site/feedbacks", CronSiteOrderFeedbacks)
			// cronGroup.GET("site/incidents", CronSiteOrderIncidentList)
			// cronGroup.GET("healths", internalRequired, CronTokenAccountHealths)
			// cronGroup.GET("auto-confirms", internalRequired, AutoConfirmOrder)
			cronGroup.GET("notifications", internalRequired, handlers.CronOrderNotificationsHandler)
		}

		// // Payment Integration Callbacks
		// api.POST("zalo/callbacks", ZaloCallback)
		// api.GET("zalo/client-callbacks", ZaloClientCallback)
		// api.GET("momo/client-callbacks", MomoClientCallback)
		// api.Any("momo/callbacks", MomoServerCallback)
		// api.GET("payos/client-callbacks", PayosClientCallback)
		// api.Any("payos/callbacks", PayosServerCallback)
		// api.GET("nexdorpay/client-callbacks", NexdorpayClientCallback)
		// api.Any("nexdorpay/callbacks", NexdorpayServerCallback)

		// // V1 API Group
		// v1Group := api.Group("v1")
		// {
		// 	// Partner Integration
		// 	v1Group.POST("partner/orders", authByAPIKey(), CreatePartnerOrder)
		// 	v1Group.Any("partners/:partner_id/update-order-status", UpdateOrderStatusWebhook)

		// 	// Grab Integration
		// 	grabGroup := v1Group.Group("partners/grab")
		// 	{
		// 		grabGroup.POST("oauth/token", GetGrabAccessToken)
		// 		grabGroup.GET("merchant/menu", GetGrabMartMenuV2)
		// 		grabGroup.Any("webhook/orders", GrabOrderWebhook)
		// 		grabGroup.Any("webhook/menu", GrabMenuWebhook)
		// 	}

		// 	// Shopee Integration
		// 	shopeeGroup := v1Group.Group("partners/shopee")
		// 	{
		// 		shopeeGroup.POST("oauth/token", GetShopeeAccessToken)
		// 		shopeeGroup.Any("webhook/orders", ShopeeWebhook)
		// 		shopeeGroup.GET("merchant/menu", GetShopeeMartMenuV2)
		// 	}
		// }

		// for core products, another service maybe
		// // V2 API Group
		// v2Group := api.Group("v2/sites/:site_id/menu")
		// {
		// 	v2Group.GET("", required(), GetMenu)
		// 	v2Group.POST("categories", required(), CreateCategory)
		// 	v2Group.PUT("categories/:category_id", required(), UpdateCategory)
		// 	v2Group.DELETE("categories/:category_id", required(), DeleteCategory)
		// 	v2Group.POST("categories/:category_id/items", required(), CreateItem)
		// 	v2Group.PUT("categories/:category_id/items/:item_id", required(), UpdateItem)
		// 	v2Group.DELETE("categories/:category_id/items/:item_id", required(), DeleteItem)
		// 	v2Group.POST("publish", required(), PublishMenu)
		// }
	}

	return r
}
