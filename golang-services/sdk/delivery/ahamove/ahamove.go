package ahamove

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nyaruka/phonenumbers"
)

type Config struct {
	BaseURL string
	APIKey  string
}

type AhamoveClient struct {
	config Config
	client *http.Client
}

type ServiceType struct {
	Name        string
	Description string
}

type BulkOption struct {
	Code       string
	Name       string
	Dimensions models.Dimensions
	Price      float64
}

var AhamoveServiceTypes = map[string]ServiceType{
	"SGN-BIKE": {
		Name:        "<PERSON><PERSON><PERSON>",
		Description: "Giao hàng nội thành trong 1 giờ",
	},
	"SGN-EXPRESS": {
		Name:        "Siêu <PERSON>ốc - Đồ Ăn",
		Description: "Giao siêu nhanh trong 1 giờ",
	},
	"SGN-2H": {
		Name:        "2H",
		Description: "Nhận đơn trong 30 phút, giao hàng tối đa 2 giờ, tiết kiệm 20%",
	},
	"SGN-2H-PUBLIC": {
		Name:        "2H",
		Description: "Nhận đơn trong 30 phút, giao hàng tối đa 2 giờ, tiết kiệm 20%",
	},
	"SGN-SAMEDAY": {
		Name:        "4H",
		Description: "Giao trong 4 giờ, giá siêu tiết kiệm",
	},
	"SGN-AHAPRO": {
		Name:        "Siêu Cấp",
		Description: "Tài xế 5* đền bu lên tới 30 triệu",
	},
	"SGN-POOL": {
		Name:        "Siêu rẻ",
		Description: "Nhận đơn trong 30 phút, giao hàng tối đa 2 giờ, tiết kiệm 20%",
	},
}

var SGNBulkOptions = []BulkOption{
	{
		Code: "TIER_1",
		Name: "Tiêu chuẩn (50x40x50:30kg)",
		Dimensions: models.Dimensions{
			Length: 50,
			Width:  40,
			Height: 50,
			Weight: 30000,
		},
		Price: 0,
	},
	{
		Code: "TIER_2",
		Name: "Mức 1 (60x50x60:40kg)",
		Dimensions: models.Dimensions{
			Length: 60,
			Width:  50,
			Height: 60,
			Weight: 40000,
		},
		Price: 10000,
	},
	{
		Code: "TIER_3",
		Name: "Mức 2 (70x60x70:60kg)",
		Dimensions: models.Dimensions{
			Length: 70,
			Width:  60,
			Height: 70,
			Weight: 60000,
		},
		Price: 20000,
	},
	{
		Code: "TIER_4",
		Name: "Mức 3 (90x70x90:80kg)",
		Dimensions: models.Dimensions{
			Length: 90,
			Width:  70,
			Height: 90,
			Weight: 80000,
		},
		Price: 40000,
	},
}

func NewAhamoveClient() *AhamoveClient {
	var config Config
	if os.Getenv("NODE_ENV") == "prod" {
		config = Config{
			BaseURL: "https://api.ahamove.com",
			APIKey:  "286779df9a9a6c52a498f978be0a63aba4fde967",
		}
	} else {
		config = Config{
			BaseURL: "https://apistg.ahamove.com",
			APIKey:  "286779df9a9a6c52a498f978be0a63aba4fde967",
		}
	}

	return &AhamoveClient{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (a *AhamoveClient) GetToken(username, password string) (*models.ShipToken, error) {
	if username == "" {
		return nil, nil
	}

	if os.Getenv("NODE_ENV") == "dev" {
		return &models.ShipToken{
			Username:     username,
			AccessToken:  "**********",
			RefreshToken: "**********",
		}, nil
	}

	parsedNumber, err := phonenumbers.Parse(username, "VN")
	if err != nil {
		return nil, err
	}

	nationalNumber := phonenumbers.GetNationalSignificantNumber(parsedNumber)
	phoneNumber84 := fmt.Sprintf("84%s", nationalNumber)

	params := url.Values{}
	params.Add("mobile", phoneNumber84)
	params.Add("api_key", a.config.APIKey)
	params.Add("name", phoneNumber84)

	requestURL := fmt.Sprintf("%s/v1/partner/register_account?%s", a.config.BaseURL, params.Encode())

	resp, err := a.client.Get(requestURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result map[string]any
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	token, ok := result["token"].(string)
	if !ok {
		return nil, errors.New("token not found in response")
	}

	refreshToken, _ := result["refresh_token"].(string)

	return &models.ShipToken{
		Username:     phoneNumber84,
		AccessToken:  token,
		RefreshToken: refreshToken,
	}, nil
}

func (a *AhamoveClient) CheckPromoCode(token *models.ShipToken, request *models.PromoCheckRequest) (*models.PromoCheckResponse, error) {
	if token == nil || token.AccessToken == "" {
		return nil, errors.New("invalid token")
	}

	// Construct path
	path := []map[string]any{
		{
			"address":       request.From.Address,
			"short_address": request.From.Address,
			"name":          request.From.Name,
			"mobile":        request.From.Phone,
			"cod":           0,
			"item_value":    0,
		},
		{
			"address":          request.To.Address,
			"short_address":    request.To.Address,
			"name":             request.To.Name,
			"mobile":           request.To.Phone,
			"cod":              0,
			"item_description": "Thực phẩm,Hàng tiêu dùng",
			"item_descriptions": []map[string]any{
				{
					"code":    "673",
					"keyword": "Thực phẩm",
					"group":   "food",
				},
				{
					"code":    "675",
					"keyword": "Hàng tiêu dùng",
					"group":   "grocery",
				},
			},
			"item_value": 0,
		},
	}

	// Create request data
	data := map[string]any{
		"payment_method": "CASH",
		"promo_code":     request.PromoCode,
		"order_time":     0,
		"path":           path,
		"images":         []any{},
		"package_detail": []any{},
		"services":       request.Services,
		"token":          token.AccessToken,
	}

	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s/v2/order/estimated_fee", a.config.BaseURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := a.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Check if response is valid
	var responseObj any
	if err := json.Unmarshal(body, &responseObj); err != nil {
		return nil, err
	}

	return &models.PromoCheckResponse{
		Valid: resp.StatusCode == http.StatusOK,
		Data:  responseObj,
	}, nil
}

func (a *AhamoveClient) CreateOrder(token *models.ShipToken, request *models.CreateShipmentRequest) (*models.CreateShipmentResponse, error) {
	if token == nil || token.AccessToken == "" {
		return nil, errors.New("invalid token")
	}

	// Initialize services array
	requestServices := []map[string]any{}

	// Add extra services if needed
	if len(request.Service.ExtraServices) > 0 {
		for _, extraService := range request.Service.ExtraServices {
			requestServices = append(requestServices, map[string]any{
				"_id":       fmt.Sprintf("%s-BULKY", request.Service.Code),
				"tier_code": extraService.Code,
			})
		}
	}

	// Construct path
	path := []map[string]any{
		{
			"address": request.From.Address,
			"name":    request.From.Name,
			"mobile":  request.From.Phone,
			"cod":     0,
		},
		{
			"address":         request.To.Address,
			"name":            request.To.Name,
			"mobile":          request.To.Phone,
			"cod":             request.COD,
			"tracking_number": request.TrackingNumber,
		},
	}

	// Format items
	items := make([]map[string]any, len(request.Dishes))
	for i, dish := range request.Dishes {
		items[i] = map[string]any{
			"_id":   fmt.Sprintf("dish-%d", i+1),
			"num":   dish.Quantity,
			"name":  dish.Name,
			"price": dish.DiscountPrice,
		}
	}

	// Create form data
	formData := url.Values{}
	formData.Set("requests", mustMarshalJSON(requestServices))
	formData.Set("service_id", request.Service.Code)
	formData.Set("payment_method", "BALANCE")
	formData.Set("promo_code", request.PromoCode)
	formData.Set("remarks", request.Note)
	formData.Set("order_time", "0")
	formData.Set("path", mustMarshalJSON(path))
	formData.Set("items", mustMarshalJSON(items))
	formData.Set("token", token.AccessToken)

	// Handle scheduled orders
	if request.ScheduleOrderTime > 0 {
		currentTime := time.Now().Unix()
		scheduleTime := request.ScheduleOrderTime

		if scheduleTime <= currentTime+60 { // 1 minute in seconds
			scheduleTime = currentTime + 60
		}

		formData.Set("order_time", fmt.Sprintf("%d", scheduleTime))
		formData.Set("idle_until", fmt.Sprintf("%d", scheduleTime))
	}

	// Send request
	url := fmt.Sprintf("%s/v1/order/create", a.config.BaseURL)
	req, err := http.NewRequest("POST", url, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := a.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse response
	var responseMap map[string]any
	if err := json.Unmarshal(body, &responseMap); err != nil {
		return nil, err
	}

	// Check for errors
	if resp.StatusCode != http.StatusOK {
		description := "Error from Ahamove"
		if errDesc, ok := responseMap["description"].(string); ok {
			description = fmt.Sprintf("Ahamove: %s", errDesc)
		}
		return nil, errors.New(description)
	}

	// Extract order data
	orderId, ok := responseMap["order_id"].(string)
	if !ok {
		return nil, errors.New("order_id not found in response")
	}

	sharedLink, _ := responseMap["shared_link"].(string)

	// Get total price
	var totalPrice float64
	if order, ok := responseMap["order"].(map[string]any); ok {
		if price, ok := order["total_price"].(float64); ok {
			totalPrice = price
		}
	}

	return &models.CreateShipmentResponse{
		ShipmentID:  orderId,
		TrackingURL: sharedLink,
		Price:       totalPrice,
		RawRequest:  formData,
		RawResponse: responseMap,
	}, nil
}

func (a *AhamoveClient) CancelOrder(token *models.ShipToken, shipmentID string) (*models.CancelShipmentResponse, error) {
	if token == nil || token.AccessToken == "" {
		return nil, errors.New("invalid token")
	}

	// Check order status first
	detail, err := a.GetOrderDetail(token, shipmentID)
	if err != nil {
		return nil, err
	}

	// If already cancelled, return success
	if detail.Status == "CANCELLED" {
		return &models.CancelShipmentResponse{
			Success: true,
			Message: "Đơn hàng đã bị hủy trước đó",
		}, nil
	}

	// Prepare params
	params := url.Values{}
	params.Add("token", token.AccessToken)
	params.Add("order_id", shipmentID)
	params.Add("comment", "User changes pick up")
	params.Add("cancel_code", "user_incorrect_pickup")

	// Send request
	url := fmt.Sprintf("%s/v1/order/cancel?%s", a.config.BaseURL, params.Encode())
	resp, err := a.client.Get(url)
	if err != nil {
		return &models.CancelShipmentResponse{
			Success: false,
			Message: fmt.Sprintf("Error from ahamove: %s", err.Error()),
		}, nil
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return &models.CancelShipmentResponse{
			Success: false,
			Message: fmt.Sprintf("Error reading response: %s", err.Error()),
		}, nil
	}

	// Parse response
	var responseMap map[string]any
	if err := json.Unmarshal(body, &responseMap); err != nil {
		return &models.CancelShipmentResponse{
			Success: false,
			Message: fmt.Sprintf("Error parsing response: %s", err.Error()),
		}, nil
	}

	// Check for success
	return &models.CancelShipmentResponse{
		Success: resp.StatusCode == http.StatusOK,
		Message: "",
	}, nil
}

func (a *AhamoveClient) GetOrderDetail(token *models.ShipToken, shipmentID string) (*models.ShipmentDetail, error) {
	if token == nil || token.AccessToken == "" {
		return nil, errors.New("invalid token")
	}

	// Prepare params
	params := url.Values{}
	params.Add("token", token.AccessToken)
	params.Add("order_id", shipmentID)

	// Send request
	url := fmt.Sprintf("%s/v1/order/detail?%s", a.config.BaseURL, params.Encode())
	resp, err := a.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse response
	var responseMap map[string]any
	if err := json.Unmarshal(body, &responseMap); err != nil {
		return nil, err
	}

	// Extract status
	status, _ := responseMap["status"].(string)

	return &models.ShipmentDetail{
		Status:  status,
		RawData: responseMap,
	}, nil
}

func (a *AhamoveClient) GetShipments(token *models.ShipToken, request *models.GetShipmentsRequest) ([]models.ShipmentOption, error) {
	if token == nil || token.AccessToken == "" {
		return []models.ShipmentOption{}, nil
	}

	// Set default service types
	serviceTypes := []string{"SGN-BIKE", "SGN-2H", "SGN-EXPRESS"}

	// Override with site data if available
	if token.SiteData != nil {
		if siteServiceTypes, ok := token.SiteData["service_types"].(string); ok && siteServiceTypes != "" {
			serviceTypes = strings.Split(siteServiceTypes, "|")
			for i, st := range serviceTypes {
				serviceTypes[i] = strings.TrimSpace(st)
			}
		}
	}

	// Calculate order time - using int64 for Unix timestamps
	var orderTime int64 = 0
	currentHour := time.Now().Hour()
	if currentHour >= 20 {
		// Set to 9AM the next day
		tomorrow := time.Now().AddDate(0, 0, 1)
		orderTime = time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 9, 0, 0, 0, time.Local).Unix()
	}

	// Prepare services
	services := make([]map[string]any, len(serviceTypes))
	for i, serviceType := range serviceTypes {
		services[i] = map[string]any{
			"_id":      serviceType,
			"requests": []any{},
		}
	}

	// Prepare request data
	requestData := map[string]any{
		"token":          token.AccessToken,
		"services":       services,
		"order_time":     orderTime,
		"idle_until":     orderTime,
		"payment_method": "cash",
		"path": []map[string]any{
			{
				"address":       request.From.Address,
				"short_address": request.From.Address,
				"name":          request.From.Name,
				"mobile":        request.From.Phone,
				"cod":           0,
				"item_value":    0,
			},
			{
				"address":          request.To.Address,
				"short_address":    request.To.Address,
				"name":             request.To.Name,
				"mobile":           request.To.Phone,
				"cod":              0,
				"item_description": "Thực phẩm,Hàng tiêu dùng",
				"item_descriptions": []map[string]any{
					{
						"code":    "673",
						"keyword": "Thực phẩm",
						"group":   "food",
					},
					{
						"code":    "675",
						"keyword": "Hàng tiêu dùng",
						"group":   "grocery",
					},
				},
				"item_value": 0,
			},
		},
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return []models.ShipmentOption{}, err
	}

	// Send request
	url := fmt.Sprintf("%s/v2/order/estimated_fee?lang=vi", a.config.BaseURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return []models.ShipmentOption{}, err
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := a.client.Do(req)
	if err != nil {
		return []models.ShipmentOption{}, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return []models.ShipmentOption{}, err
	}

	// Parse response
	var responseArray []map[string]any
	if err := json.Unmarshal(body, &responseArray); err != nil {
		return []models.ShipmentOption{}, err
	}

	// Map response to shipment options - pass dimensions directly since it's already a models.Dimensions struct
	shipmentOptions := make([]models.ShipmentOption, len(responseArray))
	for i, item := range responseArray {
		// No need to convert request.Dimensions anymore since it's already the right type
		shipmentOptions[i] = a.mappingShipment(item, request.Dimensions)
	}

	return shipmentOptions, nil
}

func (a *AhamoveClient) GetStore(token *models.ShipToken) (any, error) {
	if token == nil || token.AccessToken == "" {
		return nil, nil
	}

	// Prepare params
	params := url.Values{}
	params.Add("token", token.AccessToken)

	// Send request
	url := fmt.Sprintf("%s/api/v3/private/user/self?%s", a.config.BaseURL, params.Encode())
	resp, err := a.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse response
	var result any
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	return result, nil
}

func (a *AhamoveClient) ProcessWebhook(data map[string]any) (*models.WebhookResponse, error) {
	id, _ := data["_id"].(string)
	status, _ := data["status"].(string)
	supplierID, _ := data["supplier_id"].(string)
	supplierName, _ := data["supplier_name"].(string)
	sharedLink, _ := data["shared_link"].(string)
	cancelCode, _ := data["cancel_code"].(string)
	cancelComment, _ := data["cancel_comment"].(string)
	rebroadcastComment, _ := data["rebroadcast_comment"].(string)

	// Status mapping
	statusMap := map[string]struct {
		OrderStatus    string
		ShipmentStatus string
	}{
		"ASSIGNING":  {"DOING", "DRIVER_ASSIGNING"},
		"ACCEPTED":   {"DOING", "DRIVER_PICKING_UP"},
		"IN PROCESS": {"PICK", "IN_DELIVERY"},
		"COMPLETED":  {"FINISH", "COMPLETED"},
		"CANCELLED":  {"", "CANCELLED"},
	}

	// Cancel reason mapping
	cancelMap := map[string]struct {
		CancelBy     string
		CancelType   string
		CancelReason string
	}{
		"supplier_driver_not_available":     {"driver", "", "Không tìm được tài xế"},
		"supplier_not_enough_advance_money": {"driver", "", "Tài xế không đủ tiền ứng trước"},
		"supplier_sender_cancel":            {"driver", "", "Tài xế hủy cuốc"},
		"supplier_unable_to_contact_sender": {"driver", "", "Tài xế không liên hệ được với người gửi"},
		"supplier_wait_for_pickup_too_long": {"driver", "", "Tài xế đợi lấy hàng quá lâu"},
		"user_incorrect_pickup":             {"merchant", "", "Quán hủy đơn"},
	}

	// Get status info
	statusInfo, exists := statusMap[status]
	if !exists {
		statusInfo = statusMap["CANCELLED"] // Default to cancelled if unknown status
	}

	result := &models.WebhookResponse{
		ShipmentID:     id,
		OrderID:        "",
		ShipmentStatus: statusInfo.ShipmentStatus,
		OrderStatus:    statusInfo.OrderStatus,
		DriverName:     supplierName,
		DriverPhone:    supplierID,
		TrackingURL:    sharedLink,
	}

	// Add cancel info if the order was cancelled
	if statusInfo.ShipmentStatus == "CANCELLED" {
		cancel, exists := cancelMap[cancelCode]
		if exists {
			result.Cancel = &models.CancelInfo{
				CancelBy:     cancel.CancelBy,
				CancelType:   cancel.CancelType,
				CancelReason: fmt.Sprintf("%s (%s), Chi tiết: %s %s", cancelCode, cancel.CancelReason, cancelComment, rebroadcastComment),
			}
		} else {
			result.Cancel = &models.CancelInfo{
				CancelBy:     "system",
				CancelType:   "",
				CancelReason: fmt.Sprintf("%s, Chi tiết: %s %s", cancelCode, cancelComment, rebroadcastComment),
			}
		}
	}

	return result, nil
}

func (a *AhamoveClient) mappingShipment(item map[string]any, dimensions models.Dimensions) models.ShipmentOption {
	// Initialize extra services
	extraServices := []models.ExtraService{}

	// Handle bulky items for SGN-BIKE
	serviceID, _ := item["_id"].(string)
	if serviceID == "SGN-BIKE" {
		// Check if dimensions are specified
		if dimensions.Width > 0 && dimensions.Length > 0 && dimensions.Height > 0 && dimensions.Weight > 0 {
			// Filter available tiers based on dimensions
			availableTiers := []BulkOption{}
			for _, option := range SGNBulkOptions {
				if option.Dimensions.Width*option.Dimensions.Length*option.Dimensions.Height >=
					dimensions.Width*dimensions.Length*dimensions.Height &&
					option.Dimensions.Weight >= dimensions.Weight {
					availableTiers = append(availableTiers, option)
				}
			}

			// Sort by price (ascending)
			if len(availableTiers) > 0 {
				// Simple bubble sort for just a few items
				for i := 0; i < len(availableTiers)-1; i++ {
					for j := 0; j < len(availableTiers)-i-1; j++ {
						if availableTiers[j].Price > availableTiers[j+1].Price {
							availableTiers[j], availableTiers[j+1] = availableTiers[j+1], availableTiers[j]
						}
					}
				}

				// Add the cheapest option if it has a price
				if availableTiers[0].Price > 0 {
					extraServices = append(extraServices, models.ExtraService{
						Name:  availableTiers[0].Name,
						Code:  availableTiers[0].Code,
						Price: availableTiers[0].Price,
					})
				}
			}
		}
	}

	// Calculate total price
	totalPrice, _ := item["total_price"].(float64)
	extraServicesPrice := 0.0
	for _, service := range extraServices {
		extraServicesPrice += service.Price
	}
	totalPrice += extraServicesPrice

	// Map service code to group code
	groupCodeMap := map[string]string{
		"SGN-BIKE":      "INSTANT",
		"SGN-EXPRESS":   "INSTANT",
		"SGN-2H":        "INSTANT",
		"SGN-2H-PUBLIC": "INSTANT",
		"SGN-SAMEDAY":   "SAME_DAY",
		"SGN-AHAPRO":    "INSTANT",
		"SGN-POOL":      "INSTANT",
	}

	groupCode := groupCodeMap[serviceID]

	// Get service details
	serviceInfo, exists := AhamoveServiceTypes[serviceID]
	if !exists {
		serviceInfo = ServiceType{
			Name:        serviceID,
			Description: "Unknown service type",
		}
	}

	// Create description with extra services if any
	description := serviceInfo.Description
	if len(extraServices) > 0 {
		extraServiceNames := []string{}
		for _, service := range extraServices {
			extraServiceNames = append(extraServiceNames, service.Name)
		}
		description = fmt.Sprintf("%s + %s", description, strings.Join(extraServiceNames, ", "))
	}

	// Calculate schedule times for the raw data
	now := time.Now()
	fromTime := now.Add(1 * time.Minute)
	duration, _ := item["duration"].(float64)
	toTime := fromTime.Add(time.Duration(duration) * time.Second)

	// Create raw data with schedule information
	rawData := map[string]any{
		"schedule": map[string]any{
			"from_time":      fromTime.Format("15:04"),
			"to_time":        toTime.Format("15:04"),
			"from_date_time": fromTime.Format(time.RFC3339),
			"to_date_time":   toTime.Format(time.RFC3339),
		},
	}

	// Copy all original properties
	for k, v := range item {
		rawData[k] = v
	}

	return models.ShipmentOption{
		Vendor:        "ahamove",
		Code:          serviceID,
		GroupCode:     groupCode,
		Price:         totalPrice,
		Name:          serviceInfo.Name,
		Description:   description,
		ExtraServices: extraServices,
		Raw:           rawData,
	}
}

// Helper function to marshal JSON
func mustMarshalJSON(v any) string {
	bytes, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}
	return string(bytes)
}
