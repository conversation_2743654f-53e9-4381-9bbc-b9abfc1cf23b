package delivery

import (
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// Provider represents the interface for shipping service providers
type Provider interface {
	// GetToken gets authentication token
	GetToken(username, password string) (*models.ShipToken, error)

	// CheckPromoCode validates a promo code for a shipment
	CheckPromoCode(token *models.ShipToken, request *models.PromoCheckRequest) (*models.PromoCheckResponse, error)

	// CreateOrder creates a new shipment order
	CreateOrder(token *models.ShipToken, request *models.CreateShipmentRequest) (*models.CreateShipmentResponse, error)

	// CancelOrder cancels an existing shipment order
	CancelOrder(token *models.ShipToken, shipmentID string) (*models.CancelShipmentResponse, error)

	// GetOrderDetail retrieves details of a specific shipment
	GetOrderDetail(token *models.ShipToken, shipmentID string) (*models.ShipmentDetail, error)

	// GetShipments retrieves available shipment options
	GetShipments(token *models.ShipToken, request *models.GetShipmentsRequest) ([]models.ShipmentOption, error)

	// GetStore retrieves store information
	GetStore(token *models.ShipToken) (any, error)

	// ProcessWebhook processes webhook data from the shipping provider
	ProcessWebhook(data map[string]any) (*models.WebhookResponse, error)
}

// Common HTTP client for delivery service requests
var HttpClient = &http.Client{
	Timeout: 30 * time.Second,
}

// NewProvider creates a new delivery provider based on the vendor name
func NewProvider(vendor string) Provider {
	switch vendor {
	case "ahamove":
		// Import is handled separately to avoid circular dependencies
		return createAhamoveClient()
	case "grab_express", "grab_express_2h":
		// Import is handled separately to avoid circular dependencies
		return createGrabExpressClient()
	case "viettel_post":
		// Import is handled separately to avoid circular dependencies
		return createViettelPostClient()
	default:
		return nil
	}
}

// Functions to avoid circular dependencies
func createAhamoveClient() Provider {
	// This will be implemented in a factory file
	return nil
}

func createGrabExpressClient() Provider {
	// This will be implemented in a factory file
	return nil
}

func createViettelPostClient() Provider {
	// This will be implemented in a factory file
	return nil
}
