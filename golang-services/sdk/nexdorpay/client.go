// Package nexdorpay provides a client for interacting with the NexDorPay payment service
package nexdorpay

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

// Client represents a NexDorPay service client
type Client struct {
	// BaseURL is the base URL of the NexDorPay service
	BaseURL string
	// HTTPClient is the underlying HTTP client
	HTTPClient *resty.Client
}

// TransactionRequest represents a payment transaction request to NexDorPay
type TransactionRequest struct {
	// Amount in VND
	Amount int `json:"amount"`
	// OrderID identifies the order being paid for
	OrderID string `json:"order_id"`
	// TransactionID uniquely identifies this payment transaction
	TransactionID string `json:"transaction_id"`
	// ClientCallback URL to redirect the user after payment completion
	ClientCallback string `json:"client_callback"`
	// ServerCallback URL for server-side notifications (optional)
	ServerCallback string `json:"server_callback,omitempty"`
}

// TransactionResponse represents a response from the NexDorPay service
type TransactionResponse struct {
	Success bool `json:"success"`
	Data    struct {
		// QRCode URL to the generated QR code image
		QRCode string `json:"qrcode"`
		// PayURL is the payment page URL
		PayURL string `json:"pay_url"`
	} `json:"data"`
	Message string `json:"message,omitempty"`
}

// Transaction represents a transaction from NexDorPay
type Transaction struct {
	// Amount in VND
	Amount int `json:"amount"`
	// OrderID identifies the order being paid for
	OrderID string `json:"order_id"`
	// TransactionID uniquely identifies this payment transaction
	TransactionID string `json:"transaction_id"`
	// ClientCallback URL to redirect the user after payment completion
	ClientCallback string `json:"client_callback"`
	// ServerCallback URL for server-side notifications
	ServerCallback string `json:"server_callback"`
	// QRCode URL to the generated QR code image
	QRCode string `json:"qrcode"`
	// Status of the transaction (e.g., "PENDING", "COMPLETED")
	Status string `json:"status"`
	// ExpiredAt is when the transaction expires
	ExpiredAt *time.Time `json:"expired_at"`
	// CreatedAt is when the transaction was created
	CreatedAt time.Time `json:"created_at"`
}

// New creates a new NexDorPay client
func New(baseURL string) *Client {
	return &Client{
		BaseURL:    baseURL,
		HTTPClient: resty.New(),
	}
}

// CreateTransaction creates a new payment transaction
func (c *Client) CreateTransaction(req *TransactionRequest) (*TransactionResponse, error) {
	// Send request to create transaction
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		Post(fmt.Sprintf("%s/api/transactions", c.BaseURL))

	if err != nil {
		return nil, fmt.Errorf("failed to create transaction: %w", err)
	}

	// Parse response
	var result TransactionResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check for success
	if !result.Success {
		return nil, fmt.Errorf("transaction creation failed: %s", result.Message)
	}

	return &result, nil
}

// GetTransaction retrieves transaction details
func (c *Client) GetTransaction(transactionID string) (*Transaction, error) {
	// Send request to get transaction details
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		Get(fmt.Sprintf("%s/api/transactions/%s", c.BaseURL, transactionID))

	if err != nil {
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	// Parse response
	var result struct {
		Success bool        `json:"success"`
		Data    Transaction `json:"data"`
		Message string      `json:"message,omitempty"`
	}

	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	// Check for success
	if !result.Success {
		return nil, fmt.Errorf("failed to get transaction: %s", result.Message)
	}

	return &result.Data, nil
}
