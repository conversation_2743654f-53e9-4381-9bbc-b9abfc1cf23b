package payment

import (
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// PaymentProvider represents the interface for payment service providers
type PaymentProvider interface {
	// CreatePaymentLink creates a new payment transaction and returns payment URL
	CreatePaymentLink(token *models.Token, request *PaymentRequest) (*PaymentResponse, error)

	// GetTransactionStatus retrieves the status of a payment transaction
	GetTransactionStatus(token *models.Token, transactionID string) (*TransactionStatus, error)

	// ProcessCallback processes webhook/callback data from the payment provider
	ProcessCallback(data map[string]interface{}) (*CallbackResponse, error)

	// ValidateSignature validates the signature of callback data
	ValidateSignature(token *models.Token, data map[string]interface{}, signature string) bool

	// GetProviderName returns the name of the payment provider
	GetProviderName() string
}

// PaymentRequest represents a generic payment request
type PaymentRequest struct {
	OrderID        string  `json:"order_id" binding:"required"`
	Amount         float64 `json:"amount" binding:"required"`
	Currency       string  `json:"currency,omitempty"`
	Description    string  `json:"description,omitempty"`
	ClientCallback string  `json:"client_callback,omitempty"`
	ServerCallback string  `json:"server_callback,omitempty"`
	PaymentMethod  string  `json:"payment_method,omitempty"` // For MOMO variants: MOMO, MOMO_VTS, MOMO_ATM, MOMO_MOD
	ExtraData      string  `json:"extra_data,omitempty"`
	Language       string  `json:"language,omitempty"`
}

// PaymentResponse represents a generic payment response
type PaymentResponse struct {
	Success       bool   `json:"success"`
	TransactionID string `json:"transaction_id"`
	PayURL        string `json:"pay_url"`
	QRCode        string `json:"qr_code,omitempty"`
	Deeplink      string `json:"deeplink,omitempty"`
	Message       string `json:"message,omitempty"`
	Data          any    `json:"data,omitempty"`
}

// TransactionStatus represents the status of a payment transaction
type TransactionStatus struct {
	TransactionID string     `json:"transaction_id"`
	OrderID       string     `json:"order_id"`
	Amount        float64    `json:"amount"`
	Status        string     `json:"status"` // PENDING, COMPLETED, CANCELLED, FAILED
	Message       string     `json:"message,omitempty"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	ExpiredAt     *time.Time `json:"expired_at,omitempty"`
	Data          any        `json:"data,omitempty"`
}

// CallbackResponse represents the response from processing a callback
type CallbackResponse struct {
	Success       bool    `json:"success"`
	TransactionID string  `json:"transaction_id"`
	OrderID       string  `json:"order_id"`
	Status        string  `json:"status"`
	Amount        float64 `json:"amount"`
	Message       string  `json:"message,omitempty"`
	Data          any     `json:"data,omitempty"`
}

// Common HTTP client for payment service requests
var HttpClient = &http.Client{
	Timeout: 30 * time.Second,
}

// NewProvider creates a new payment provider based on the vendor name
func NewProvider(vendor string) PaymentProvider {
	switch vendor {
	case "momo":
		return createMomoClient()
	case "nexdorpay":
		return createNexdorpayClient()
	case "payos":
		return createPayosClient()
	default:
		return nil
	}
}

// Factory function variables to avoid circular dependencies
var (
	createMomoClient = func() PaymentProvider {
		return NewMomoClient()
	}

	createNexdorpayClient = func() PaymentProvider {
		return NewNexdorpayClient()
	}

	createPayosClient = func() PaymentProvider {
		return NewPayosClient()
	}
)
