package payment

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// PayosClient represents a PayOS payment client
type PayosClient struct {
	BaseURL    string
	HTTPClient *resty.Client
}

// PayOSRequest represents a PayOS payment request
type PayOSRequest struct {
	OrderCode   int64  `json:"orderCode"`
	Amount      int64  `json:"amount"`
	Description string `json:"description"`
	CancelURL   string `json:"cancelUrl"`
	ReturnURL   string `json:"returnUrl"`
	ExpiredAt   int64  `json:"expiredAt"`
	Signature   string `json:"signature"`
}

// PayOSResponse represents a PayOS payment response
type PayOSResponse struct {
	Error int `json:"error"`
	Data  struct {
		Bin           string `json:"bin"`
		AccountNumber string `json:"accountNumber"`
		AccountName   string `json:"accountName"`
		Amount        int64  `json:"amount"`
		Description   string `json:"description"`
		OrderCode     int64  `json:"orderCode"`
		Currency      string `json:"currency"`
		PaymentLinkID string `json:"paymentLinkId"`
		Status        string `json:"status"`
		CheckoutURL   string `json:"checkoutUrl"`
		QRCode        string `json:"qrCode"`
	} `json:"data"`
	Message string `json:"message"`
}

// PayOSStatusResponse represents a PayOS transaction status response
type PayOSStatusResponse struct {
	Error int `json:"error"`
	Data  struct {
		ID                    string     `json:"id"`
		OrderCode             int64      `json:"orderCode"`
		Amount                int64      `json:"amount"`
		AmountPaid            int64      `json:"amountPaid"`
		AmountRemaining       int64      `json:"amountRemaining"`
		Status                string     `json:"status"`
		CreatedAt             time.Time  `json:"createdAt"`
		CanceledAt            *time.Time `json:"canceledAt"`
		CancellationReason    string     `json:"cancellationReason"`
		Transactions          []any      `json:"transactions"`
	} `json:"data"`
	Message string `json:"message"`
}

// PayOSItem represents an item in PayOS payment
type PayOSItem struct {
	Name     string `json:"name"`
	Quantity int    `json:"quantity"`
	Price    int64  `json:"price"`
}

// NewPayosClient creates a new PayOS payment client
func NewPayosClient() *PayosClient {
	return &PayosClient{
		BaseURL:    "https://api-merchant.payos.vn",
		HTTPClient: resty.New(),
	}
}

// GetProviderName returns the provider name
func (c *PayosClient) GetProviderName() string {
	return "payos"
}

// CreatePaymentLink creates a new PayOS payment transaction
func (c *PayosClient) CreatePaymentLink(token *models.Token, request *PaymentRequest) (*PaymentResponse, error) {
	// Generate order code (reference ID)
	orderCode := c.generateOrderCode(request.OrderID)

	// Prepare PayOS request
	payosReq := PayOSRequest{
		OrderCode:   orderCode,
		Amount:      int64(request.Amount),
		Description: c.getDescription(request.OrderID, request.Description),
		CancelURL:   request.ClientCallback,
		ReturnURL:   request.ClientCallback,
		ExpiredAt:   time.Now().Add(1 * time.Hour).Unix(),
	}

	// Generate signature
	signature := c.generateSignature(payosReq, token.SiteID)
	payosReq.Signature = signature

	// Send request
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-client-id", token.Username).
		SetHeader("x-api-key", token.Password).
		SetBody(payosReq).
		Post(c.BaseURL + "/v2/payment-requests")

	if err != nil {
		return nil, fmt.Errorf("failed to create PayOS payment: %w", err)
	}

	// Parse response
	var payosResp PayOSResponse
	if err := json.Unmarshal(resp.Body(), &payosResp); err != nil {
		return nil, fmt.Errorf("failed to parse PayOS response: %w", err)
	}

	// Check for errors
	if payosResp.Error != 0 {
		return nil, fmt.Errorf("PayOS payment creation failed: %s", payosResp.Message)
	}

	return &PaymentResponse{
		Success:       true,
		TransactionID: payosResp.Data.PaymentLinkID,
		PayURL:        payosResp.Data.CheckoutURL,
		QRCode:        payosResp.Data.QRCode,
		Message:       payosResp.Message,
		Data:          payosResp,
	}, nil
}

// GetTransactionStatus retrieves PayOS transaction status
func (c *PayosClient) GetTransactionStatus(token *models.Token, transactionID string) (*TransactionStatus, error) {
	// Send request to get payment link info
	resp, err := c.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("x-client-id", token.Username).
		SetHeader("x-api-key", token.Password).
		Get(fmt.Sprintf("%s/v2/payment-requests/%s", c.BaseURL, transactionID))

	if err != nil {
		return nil, fmt.Errorf("failed to get PayOS transaction: %w", err)
	}

	// Parse response
	var statusResp PayOSStatusResponse
	if err := json.Unmarshal(resp.Body(), &statusResp); err != nil {
		return nil, fmt.Errorf("failed to parse PayOS status response: %w", err)
	}

	// Check for errors
	if statusResp.Error != 0 {
		return nil, fmt.Errorf("failed to get PayOS transaction: %s", statusResp.Message)
	}

	status := c.mapStatus(statusResp.Data.Status)

	return &TransactionStatus{
		TransactionID: statusResp.Data.ID,
		OrderID:       fmt.Sprintf("%d", statusResp.Data.OrderCode),
		Amount:        float64(statusResp.Data.Amount),
		Status:        status,
		Message:       statusResp.Message,
		CreatedAt:     statusResp.Data.CreatedAt,
		UpdatedAt:     statusResp.Data.CreatedAt, // PayOS doesn't provide separate updated_at
		Data:          statusResp.Data,
	}, nil
}

// ProcessCallback processes PayOS callback data
func (c *PayosClient) ProcessCallback(data map[string]interface{}) (*CallbackResponse, error) {
	code, _ := data["code"].(string)
	paymentData, _ := data["data"].(map[string]interface{})

	var transactionID string
	var orderCode float64
	var amount float64

	if paymentData != nil {
		if linkID, ok := paymentData["paymentLinkId"].(string); ok {
			transactionID = linkID
		}
		if oc, ok := paymentData["orderCode"].(float64); ok {
			orderCode = oc
		}
		if amt, ok := paymentData["amount"].(float64); ok {
			amount = amt
		}
	}

	// Map PayOS status
	mappedStatus := c.mapCallbackStatus(code)
	success := code == "00"

	return &CallbackResponse{
		Success:       success,
		TransactionID: transactionID,
		OrderID:       fmt.Sprintf("%.0f", orderCode),
		Status:        mappedStatus,
		Amount:        amount,
		Message:       fmt.Sprintf("PayOS callback with code: %s", code),
		Data:          data,
	}, nil
}

// ValidateSignature validates PayOS callback signature
func (c *PayosClient) ValidateSignature(token *models.Token, data map[string]interface{}, signature string) bool {
	// Implementation would depend on PayOS's signature validation requirements
	// This is a placeholder implementation
	return true
}

// Helper methods
func (c *PayosClient) generateOrderCode(orderID string) int64 {
	// Generate a numeric order code from order ID
	// This is a simple implementation - you might want to use a more sophisticated method
	hash := 0
	for _, char := range orderID {
		hash = hash*31 + int(char)
	}
	if hash < 0 {
		hash = -hash
	}
	// Ensure it's within PayOS limits (typically 6-12 digits)
	return int64(hash % 999999999999)
}

func (c *PayosClient) getDescription(orderID, description string) string {
	if description != "" {
		return description
	}
	// Limit description to 15 characters as per Node.js implementation
	if len(orderID) > 15 {
		return fmt.Sprintf("Order %s", orderID[len(orderID)-15:])
	}
	return fmt.Sprintf("Order %s", orderID)
}

func (c *PayosClient) mapStatus(status string) string {
	switch status {
	case "PAID":
		return "COMPLETED"
	case "CANCELLED":
		return "CANCELLED"
	case "PENDING":
		return "PENDING"
	case "PROCESSING":
		return "PENDING"
	default:
		return "PENDING"
	}
}

func (c *PayosClient) mapCallbackStatus(code string) string {
	switch code {
	case "00":
		return "COMPLETED"
	case "01", "02", "03":
		return "CANCELLED"
	default:
		return "PENDING"
	}
}

func (c *PayosClient) generateSignature(req PayOSRequest, checksum string) string {
	// Create signature data based on PayOS requirements
	data := map[string]interface{}{
		"amount":      req.Amount,
		"cancelUrl":   req.CancelURL,
		"description": req.Description,
		"orderCode":   req.OrderCode,
		"returnUrl":   req.ReturnURL,
	}

	// Sort keys
	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Build signature string
	var parts []string
	for _, key := range keys {
		parts = append(parts, fmt.Sprintf("%s=%v", key, data[key]))
	}

	rawSignature := strings.Join(parts, "&")
	h := hmac.New(sha256.New, []byte(checksum))
	h.Write([]byte(rawSignature))
	return hex.EncodeToString(h.Sum(nil))
}
