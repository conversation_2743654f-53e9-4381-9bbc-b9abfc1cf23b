package payment

import (
	"crypto/hmac"
	"crypto/sha512"
	"encoding/hex"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// VNPayClient represents a VNPay payment client
type VNPayClient struct {
	BaseURL    string
	HTTPClient *resty.Client
}

// VNPayRequest represents a VNPay payment request
type VNPayRequest struct {
	Version        string `json:"vnp_Version"`
	Command        string `json:"vnp_Command"`
	TmnCode        string `json:"vnp_TmnCode"`
	Amount         int64  `json:"vnp_Amount"`
	CurrCode       string `json:"vnp_CurrCode"`
	TxnRef         string `json:"vnp_TxnRef"`
	OrderInfo      string `json:"vnp_OrderInfo"`
	OrderType      string `json:"vnp_OrderType"`
	Locale         string `json:"vnp_Locale"`
	ReturnURL      string `json:"vnp_ReturnUrl"`
	IpnURL         string `json:"vnp_IpnUrl"`
	CreateDate     string `json:"vnp_CreateDate"`
	ExpireDate     string `json:"vnp_ExpireDate"`
	BankCode       string `json:"vnp_BankCode,omitempty"`
	SecureHash     string `json:"vnp_SecureHash"`
}

// VNPayResponse represents a VNPay payment response
type VNPayResponse struct {
	RspCode    string `json:"vnp_ResponseCode"`
	Message    string `json:"vnp_Message"`
	TmnCode    string `json:"vnp_TmnCode"`
	Amount     int64  `json:"vnp_Amount"`
	BankCode   string `json:"vnp_BankCode"`
	PayDate    string `json:"vnp_PayDate"`
	TxnRef     string `json:"vnp_TxnRef"`
	TransNo    string `json:"vnp_TransactionNo"`
	SecureHash string `json:"vnp_SecureHash"`
}

// VNPayQRResponse represents a VNPay QR code response
type VNPayQRResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    struct {
		QRCode    string `json:"qrCode"`
		QRDataURL string `json:"qrDataURL"`
	} `json:"data"`
}

// NewVNPayClient creates a new VNPay payment client
func NewVNPayClient() *VNPayClient {
	return &VNPayClient{
		BaseURL:    "https://sandbox-payment.vnpay.vn", // Use production URL for live
		HTTPClient: resty.New(),
	}
}

// GetProviderName returns the provider name
func (c *VNPayClient) GetProviderName() string {
	return "vnpay"
}

// CreatePaymentLink creates a new VNPay payment transaction
func (c *VNPayClient) CreatePaymentLink(token *models.Token, request *PaymentRequest) (*PaymentResponse, error) {
	// Generate transaction reference
	txnRef := c.generateTxnRef(request.OrderID)
	
	// Create VNPay request
	vnpReq := VNPayRequest{
		Version:    "2.1.0",
		Command:    "pay",
		TmnCode:    token.SiteID,
		Amount:     int64(request.Amount * 100), // VNPay expects amount in smallest currency unit (VND * 100)
		CurrCode:   "VND",
		TxnRef:     txnRef,
		OrderInfo:  c.getOrderInfo(request.OrderID, request.Description),
		OrderType:  "other",
		Locale:     c.getLocale(request.Language),
		ReturnURL:  request.ClientCallback,
		IpnURL:     request.ServerCallback,
		CreateDate: time.Now().Format("**************"),
		ExpireDate: time.Now().Add(15 * time.Minute).Format("**************"),
		BankCode:   c.getBankCode(request.PaymentMethod),
	}

	// Generate secure hash
	secureHash := c.generateSecureHash(vnpReq, token.Password)
	vnpReq.SecureHash = secureHash

	// Build payment URL
	paymentURL := c.buildPaymentURL(vnpReq)

	// For QR code, we can use VNPay's QR API or generate a QR code from the payment URL
	qrCode := ""
	if request.PaymentMethod == "VNPAY_QR" || request.PaymentMethod == "" {
		qrCode = c.generateQRCode(paymentURL)
	}

	return &PaymentResponse{
		Success:       true,
		TransactionID: txnRef,
		PayURL:        paymentURL,
		QRCode:        qrCode,
		Message:       "VNPay payment link created successfully",
		Data: map[string]interface{}{
			"vnp_TxnRef":    txnRef,
			"vnp_Amount":    vnpReq.Amount,
			"vnp_OrderInfo": vnpReq.OrderInfo,
			"vnp_BankCode":  vnpReq.BankCode,
		},
	}, nil
}

// GetTransactionStatus retrieves VNPay transaction status
func (c *VNPayClient) GetTransactionStatus(token *models.Token, transactionID string) (*TransactionStatus, error) {
	// VNPay doesn't have a direct query API in the standard implementation
	// Transaction status is typically received via IPN (Instant Payment Notification)
	// This is a placeholder implementation
	return &TransactionStatus{
		TransactionID: transactionID,
		OrderID:       transactionID,
		Amount:        0,
		Status:        "PENDING",
		Message:       "VNPay transaction status check not implemented - use IPN callbacks",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
		Data:          nil,
	}, nil
}

// ProcessCallback processes VNPay callback data
func (c *VNPayClient) ProcessCallback(data map[string]interface{}) (*CallbackResponse, error) {
	rspCode, _ := data["vnp_ResponseCode"].(string)
	txnRef, _ := data["vnp_TxnRef"].(string)
	amount, _ := data["vnp_Amount"].(string)
	transNo, _ := data["vnp_TransactionNo"].(string)
	
	// Convert amount from string to float
	amountFloat := 0.0
	if amount != "" {
		if amountInt, err := strconv.ParseInt(amount, 10, 64); err == nil {
			amountFloat = float64(amountInt) / 100 // Convert back from smallest unit
		}
	}

	// Map VNPay response code to status
	status := c.mapResponseCode(rspCode)
	success := rspCode == "00"

	return &CallbackResponse{
		Success:       success,
		TransactionID: txnRef,
		OrderID:       txnRef,
		Status:        status,
		Amount:        amountFloat,
		Message:       c.getResponseMessage(rspCode),
		Data: map[string]interface{}{
			"vnp_TransactionNo": transNo,
			"vnp_ResponseCode":  rspCode,
			"original_data":     data,
		},
	}, nil
}

// ValidateSignature validates VNPay callback signature
func (c *VNPayClient) ValidateSignature(token *models.Token, data map[string]interface{}, signature string) bool {
	// Remove signature from data for validation
	validationData := make(map[string]interface{})
	for k, v := range data {
		if k != "vnp_SecureHash" && k != "vnp_SecureHashType" {
			validationData[k] = v
		}
	}

	// Generate expected signature
	expectedSignature := c.generateSecureHashFromMap(validationData, token.Password)
	
	return strings.EqualFold(signature, expectedSignature)
}

// Helper methods
func (c *VNPayClient) generateTxnRef(orderID string) string {
	// VNPay transaction reference should be unique and alphanumeric
	timestamp := time.Now().Format("**************")
	return fmt.Sprintf("%s_%s", orderID, timestamp)
}

func (c *VNPayClient) getOrderInfo(orderID, description string) string {
	if description != "" {
		return description
	}
	return fmt.Sprintf("Thanh toan don hang %s", orderID)
}

func (c *VNPayClient) getLocale(language string) string {
	if language == "en" {
		return "en"
	}
	return "vn" // Default to Vietnamese
}

func (c *VNPayClient) getBankCode(paymentMethod string) string {
	// Map payment methods to VNPay bank codes
	switch paymentMethod {
	case "VNPAY_QR":
		return "VNPAYQR"
	case "VNBANK":
		return "VNBANK"
	case "INTCARD":
		return "INTCARD"
	case "VISA":
		return "VISA"
	case "MASTERCARD":
		return "MASTERCARD"
	case "JCB":
		return "JCB"
	default:
		return "" // Let user choose payment method
	}
}

func (c *VNPayClient) mapResponseCode(rspCode string) string {
	switch rspCode {
	case "00":
		return "COMPLETED"
	case "07":
		return "PENDING"
	case "09":
		return "CANCELLED"
	case "10", "11", "12", "13", "24", "51", "65", "75", "79", "99":
		return "FAILED"
	default:
		return "PENDING"
	}
}

func (c *VNPayClient) getResponseMessage(rspCode string) string {
	messages := map[string]string{
		"00": "Giao dịch thành công",
		"07": "Trừ tiền thành công. Giao dịch bị nghi ngờ (liên quan tới lừa đảo, giao dịch bất thường).",
		"09": "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ InternetBanking tại ngân hàng.",
		"10": "Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần",
		"11": "Giao dịch không thành công do: Đã hết hạn chờ thanh toán. Xin quý khách vui lòng thực hiện lại giao dịch.",
		"12": "Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng bị khóa.",
		"13": "Giao dịch không thành công do Quý khách nhập sai mật khẩu xác thực giao dịch (OTP).",
		"24": "Giao dịch không thành công do: Khách hàng hủy giao dịch",
		"51": "Giao dịch không thành công do: Tài khoản của quý khách không đủ số dư để thực hiện giao dịch.",
		"65": "Giao dịch không thành công do: Tài khoản của Quý khách đã vượt quá hạn mức giao dịch trong ngày.",
		"75": "Ngân hàng thanh toán đang bảo trì.",
		"79": "Giao dịch không thành công do: KH nhập sai mật khẩu thanh toán quá số lần quy định.",
		"99": "Các lỗi khác (lỗi còn lại, không có trong danh sách mã lỗi đã liệt kê)",
	}
	
	if msg, exists := messages[rspCode]; exists {
		return msg
	}
	return "Lỗi không xác định"
}

func (c *VNPayClient) generateSecureHash(req VNPayRequest, secretKey string) string {
	// Create parameter map
	params := map[string]interface{}{
		"vnp_Version":    req.Version,
		"vnp_Command":    req.Command,
		"vnp_TmnCode":    req.TmnCode,
		"vnp_Amount":     req.Amount,
		"vnp_CurrCode":   req.CurrCode,
		"vnp_TxnRef":     req.TxnRef,
		"vnp_OrderInfo":  req.OrderInfo,
		"vnp_OrderType":  req.OrderType,
		"vnp_Locale":     req.Locale,
		"vnp_ReturnUrl":  req.ReturnURL,
		"vnp_IpnUrl":     req.IpnURL,
		"vnp_CreateDate": req.CreateDate,
		"vnp_ExpireDate": req.ExpireDate,
	}
	
	if req.BankCode != "" {
		params["vnp_BankCode"] = req.BankCode
	}

	return c.generateSecureHashFromMap(params, secretKey)
}

func (c *VNPayClient) generateSecureHashFromMap(params map[string]interface{}, secretKey string) string {
	// Sort parameters by key
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// Build query string
	var parts []string
	for _, key := range keys {
		value := fmt.Sprintf("%v", params[key])
		if value != "" {
			parts = append(parts, fmt.Sprintf("%s=%s", key, url.QueryEscape(value)))
		}
	}

	rawData := strings.Join(parts, "&")
	
	// Generate HMAC-SHA512 hash
	h := hmac.New(sha512.New, []byte(secretKey))
	h.Write([]byte(rawData))
	return strings.ToUpper(hex.EncodeToString(h.Sum(nil)))
}

func (c *VNPayClient) buildPaymentURL(req VNPayRequest) string {
	params := url.Values{}
	params.Set("vnp_Version", req.Version)
	params.Set("vnp_Command", req.Command)
	params.Set("vnp_TmnCode", req.TmnCode)
	params.Set("vnp_Amount", strconv.FormatInt(req.Amount, 10))
	params.Set("vnp_CurrCode", req.CurrCode)
	params.Set("vnp_TxnRef", req.TxnRef)
	params.Set("vnp_OrderInfo", req.OrderInfo)
	params.Set("vnp_OrderType", req.OrderType)
	params.Set("vnp_Locale", req.Locale)
	params.Set("vnp_ReturnUrl", req.ReturnURL)
	params.Set("vnp_IpnUrl", req.IpnURL)
	params.Set("vnp_CreateDate", req.CreateDate)
	params.Set("vnp_ExpireDate", req.ExpireDate)
	
	if req.BankCode != "" {
		params.Set("vnp_BankCode", req.BankCode)
	}
	
	params.Set("vnp_SecureHash", req.SecureHash)

	return fmt.Sprintf("%s/paymentv2/vpcpay.html?%s", c.BaseURL, params.Encode())
}

func (c *VNPayClient) generateQRCode(paymentURL string) string {
	// For VNPay QR code, you would typically:
	// 1. Use VNPay's QR API if available
	// 2. Generate QR code from payment URL using a QR library
	// 3. Return the QR code image URL or base64 data
	
	// This is a placeholder - in real implementation, you would generate actual QR code
	return fmt.Sprintf("https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=%s", url.QueryEscape(paymentURL))
}
