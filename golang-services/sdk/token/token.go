package token

import (
	"fmt"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"gorm.io/gorm"
)

func GetToken(site models.Site, source string) *models.SiteToken {
	for _, token := range site.Tokens {
		if token.Source == source {
			return &token
		}
	}
	return nil
}

// IsTokenWorking checks if a token is still valid
func IsTokenWorking(db *gorm.DB, tokenCode string) (bool, error) {
	// TODO
	return true, nil
}

// RefreshNewToken refreshes the token for a given token code
func RefreshNewToken(db *gorm.DB, tokenCode string) (bool, error) {
	// TODO"
	return false, fmt.Errorf("unsupported token source: %s", "source")
}

// GetTokenByCode retrieves a token account by its code
func GetTokenByCode(db *gorm.DB, tokenCode string) (*models.TokenAccount, error) {
	var account models.TokenAccount
	if err := db.Where("token_code = ?", tokenCode).First(&account).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

// GetTokenBySite retrieves a token for a specific site and source
func GetTokenBySite(db *gorm.DB, site models.Site, source string) (*models.TokenAccount, error) {
	if source == "be" {
		return GetTokenByCode(db, site.BeToken.Data.TokenCode)
	}
	if source == "shopee" {
		return GetTokenByCode(db, site.ShopeeToken.Data.TokenCode)
	}
	if source == "grab" {
		return GetTokenByCode(db, site.GrabToken.Data.TokenCode)
	}

	return &models.TokenAccount{}, nil
}
