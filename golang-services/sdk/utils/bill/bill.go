package bill

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"gorm.io/gorm"
)

// BillOptions contains options for bill generation
type BillOptions struct {
	BillType      string `json:"bill_type"`
	GenerateBill  bool   `json:"generate_bill"`
	GenerateLabel bool   `json:"generate_label"`
}

// ChannelOptions contains options for sending bills to channels
type ChannelOptions struct {
	BillTemplate  string `json:"bill_template"`
	LabelTemplate string `json:"label_template"`
	ZaloMessage   string `json:"zalo_message"`
	Zalo          bool   `json:"zalo"`
	UrgentMessage bool   `json:"urgent_message"`
	Bill          bool   `json:"bill"`
	Labels        bool   `json:"labels"`
}

// MapBillToOrderField maps bill types to order fields
var MapBillToOrderField = map[string]string{
	"bill_for_kitchen":      "bill_url",
	"bill_for_payment":      "bill_for_payment_url",
	"bill_for_complete":     "bill_for_complete_url",
	"bill_for_complete_app": "bill_for_complete_app_url",
	"bill_for_cancel":       "bill_for_cancel_url",
	"bill_for_label":        "", // This is handled separately with label_urls
}

// RenderBillHTML renders the bill HTML for a given order and bill type
func RenderBillHTML(db *gorm.DB, orderID string, billType string) (string, error) {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return "", err
	}

	// Find site and brand
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return "", err
	}

	var brand models.Brand
	if err := db.Where("id = ?", site.BrandID).First(&brand).Error; err != nil {
		return "", err
	}

	// Find hub
	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		return "", err
	}

	// Find bill config
	var billConfig models.BrandBillConfig
	if err := db.Where("brand_id = ? AND bill_type = ?", "66fa204bc13e40f109e020c7", billType).
		Where("active = ?", true).
		First(&billConfig).Error; err != nil {
		return "", err
	}

	// Get order data mapping from the DataMapping field
	dataMapping := make(map[string]interface{})
	dataMappingBytes, err := json.Marshal(order.DataMapping.Data)
	if err == nil {
		_ = json.Unmarshal(dataMappingBytes, &dataMapping)
	}

	// Get merchant info
	merchantInfo := map[string]map[string]string{
		"grab":         {"label": "Grab Food"},
		"grab_mart":    {"label": "Grab Mart"},
		"shopee":       {"label": "Shopee Food"},
		"shopee_ecom":  {"label": "Shopee Ecom"},
		"shopee_fresh": {"label": "Shopee Fresh"},
		"gojek":        {"label": "Gojek"},
		"be":           {"label": "Be"},
		"local":        {"label": "Tạo trên NexPos"},
		"he":           {"label": "Đại lý cá nhân"},
		"lazada":       {"label": "Lazada"},
		"tiktok":       {"label": "Tiktok"},
	}

	// Get order source label
	orderSourceLabel := order.Source
	if sourceInfo, ok := merchantInfo[order.Source]; ok {
		orderSourceLabel = sourceInfo["label"]
	}

	// Create replacement map
	replacement := map[string]string{
		"bill_name":     billConfig.Name,
		"long_order_id": order.OrderID,
		"site_name":     site.Name,
		"brand_name":    brand.Name,
		"order_source":  orderSourceLabel,
		"hub_name":      hub.Name,
		"hub_phone":     hub.Phone,
	}

	// Add data from order.DataMapping
	if shortOrderID, ok := dataMapping["order_id"].(string); ok {
		replacement["short_order_id"] = shortOrderID
	}

	if note, ok := dataMapping["note"].(string); ok {
		replacement["order_note"] = note
	} else {
		replacement["order_note"] = ""
	}

	// Format dates
	if orderTime, ok := dataMapping["order_time"].(string); ok {
		orderTimeFormatted, _ := time.Parse(time.RFC3339, orderTime)
		replacement["order_created_at"] = orderTimeFormatted.Format("02/01/2006 15:04:05")
	} else {
		replacement["order_created_at"] = time.Now().Format("02/01/2006 15:04:05")
	}

	if pickTime, ok := dataMapping["pick_time"].(string); ok {
		pickTimeFormatted, _ := time.Parse(time.RFC3339, pickTime)
		replacement["order_pick_at"] = pickTimeFormatted.Format("02/01/2006 15:04:05")
	}

	// Handle delivery time
	shipmentBytes, err := json.Marshal(order.Shipment.Data)
	if err == nil {
		var shipment map[string]interface{}
		if err := json.Unmarshal(shipmentBytes, &shipment); err == nil {
			if schedule, ok := shipment["schedule"].(map[string]interface{}); ok {
				if fromTime, ok := schedule["from_time"].(string); ok && fromTime != "" {
					if fromDateTime, ok := schedule["from_date_time"].(string); ok {
						fromDateTimeFormatted, _ := time.Parse(time.RFC3339, fromDateTime)
						fromStr := fromDateTimeFormatted.Format("02/01/2006 15:04")

						if toDateTime, ok := schedule["to_date_time"].(string); ok {
							toDateTimeFormatted, _ := time.Parse(time.RFC3339, toDateTime)
							toStr := toDateTimeFormatted.Format("15:04")
							replacement["order_delivery_at"] = fromStr + " - " + toStr
						} else {
							replacement["order_delivery_at"] = fromStr
						}
					}
				}
			}
		}
	}

	if _, ok := replacement["order_delivery_at"]; !ok {
		if deliveryTime, ok := dataMapping["delivery_time"].(string); ok {
			deliveryTimeFormatted, _ := time.Parse(time.RFC3339, deliveryTime)
			replacement["order_delivery_at"] = deliveryTimeFormatted.Format("02/01/2006 15:04")
		}
	}

	// Customer information
	if customerName, ok := dataMapping["customer_name"].(string); ok {
		replacement["customer_name"] = customerName
	}

	if customerAddress, ok := dataMapping["customer_address"].(string); ok {
		replacement["customer_address"] = customerAddress
	}

	// Order totals
	if total, ok := dataMapping["total"].(float64); ok {
		replacement["order_sub_total"] = FormatCurrency(total)
	}

	if totalDiscount, ok := dataMapping["total_discount"].(float64); ok {
		replacement["order_total_discount"] = FormatCurrency(totalDiscount)
	}

	if totalForBiz, ok := dataMapping["total_for_biz"].(float64); ok {
		replacement["order_total_paid"] = FormatCurrency(totalForBiz)
	}

	// Cancel reason
	if cancelReason, ok := dataMapping["cancel_reason"].(string); ok {
		replacement["cancel_reason"] = cancelReason
	} else {
		replacement["cancel_reason"] = ""
	}

	// Get the HTML content
	html := billConfig.ContentHTML

	// Process dishes if available
	dishes, ok := dataMapping["dishes"].([]interface{})
	if ok && len(dishes) > 0 {
		// Find dish template in the HTML
		dishTemplateStart := strings.Index(html, "<tr")
		if dishTemplateStart >= 0 {
			// Find the row that contains {dish_name}
			dishTemplateEnd := strings.Index(html[dishTemplateStart:], "</tr>")
			if dishTemplateEnd >= 0 {
				dishTemplateEnd += dishTemplateStart + 5 // Add length of "</tr>"

				// Extract the dish template
				dishTemplate := html[dishTemplateStart:dishTemplateEnd]

				// Check if this is actually a dish template
				if strings.Contains(dishTemplate, "{dish_name}") {
					// Generate HTML for all dishes
					var dishesHTML strings.Builder

					for _, d := range dishes {
						dish, ok := d.(map[string]interface{})
						if !ok {
							continue
						}

						// Get dish properties
						dishName := getStringValue(dish, "name")
						dishDescription := getStringValue(dish, "description")
						dishQuantity := getFloatValue(dish, "quantity")
						dishPrice := getFloatValue(dish, "price")
						dishDiscountPrice := getFloatValue(dish, "discount_price")
						dishNote := getStringValue(dish, "note")

						// Calculate totals
						dishTotal := dishPrice * dishQuantity
						dishFinalPrice := dishDiscountPrice * dishQuantity
						if dishDiscountPrice == 0 {
							dishFinalPrice = dishTotal
						}

						// Create dish replacement map
						dishReplacements := map[string]string{
							"dish_name":        dishName,
							"dish_description": dishDescription,
							"dish_quantity":    fmt.Sprintf("%.0f", dishQuantity),
							"dish_note":        dishNote,
							"dish_total":       FormatCurrency(dishTotal),
							"dish_final_price": FormatCurrency(dishFinalPrice),
						}

						// Only show original price if there's a discount
						if dishDiscountPrice != dishPrice {
							dishReplacements["dish_price"] = FormatCurrency(dishPrice)
						} else {
							dishReplacements["dish_price"] = ""
						}

						dishReplacements["dish_discount_price"] = FormatCurrency(dishDiscountPrice)

						// Apply replacements to dish template
						dishHTML := StringReplacer(dishTemplate, dishReplacements)

						// Process dish options
						if strings.Contains(dishHTML, "{dish_options}") {
							options, ok := dish["options"].([]interface{})
							if ok && len(options) > 0 {
								// Group options by name
								groupedOptions := make(map[string][]map[string]interface{})

								for _, optGroup := range options {
									optionGroup, ok := optGroup.([]interface{})
									if !ok {
										continue
									}

									for _, opt := range optionGroup {
										option, ok := opt.(map[string]interface{})
										if !ok {
											continue
										}

										optionName := getStringValue(option, "option_name")
										if optionName == "" {
											continue
										}

										if _, ok := groupedOptions[optionName]; !ok {
											groupedOptions[optionName] = []map[string]interface{}{}
										}

										groupedOptions[optionName] = append(groupedOptions[optionName], option)
									}
								}

								// Generate HTML for options
								var optionsHTML strings.Builder
								for groupName, groupOptions := range groupedOptions {
									optionsHTML.WriteString(fmt.Sprintf(`
										<div class="option-group">
											<p><span style="font-family:Arial, Helvetica, sans-serif;font-size:26px;"><i>%s</i></span></p>
									`, groupName))

									for _, opt := range groupOptions {
										optionItem := getStringValue(opt, "option_item")
										optionQuantity := getFloatValue(opt, "quantity")
										optionPrice := getFloatValue(opt, "price")

										priceStr := ""
										if optionPrice > 0 {
											priceStr = " - " + FormatCurrency(optionPrice)
										}

										optionsHTML.WriteString(fmt.Sprintf(
											`<p><span style="font-family:Arial, Helvetica, sans-serif;font-size:26px;"><i>- %.0fx %s%s</i></span></p>`,
											optionQuantity, optionItem, priceStr))
									}

									optionsHTML.WriteString(`</div>`)
								}

								// Replace dish options placeholder
								dishHTML = strings.Replace(dishHTML, "{dish_options}", optionsHTML.String(), 1)
							} else {
								// Remove dish options placeholder
								dishHTML = strings.Replace(dishHTML, "{dish_options}", "", 1)
							}
						}

						dishesHTML.WriteString(dishHTML)
					}

					// Replace the dish template with all dishes
					html = html[:dishTemplateStart] + dishesHTML.String() + html[dishTemplateEnd:]
				}
			}
		}
	}

	// Apply global replacements
	html = StringReplacer(html, replacement)

	// Add necessary styles and classes
	billSize := 590
	if billConfig.BillSize > 0 {
		billSize = billConfig.BillSize
	}

	html = strings.Replace(
		html,
		"<body>",
		fmt.Sprintf("<body class=\"ck-content\" style=\"width: %dpx; background-color: white;\">", billSize),
		1,
	)

	// Add CKEditor styles
	ckeditorStyles := "<link rel=\"stylesheet\" type=\"text/css\" href=\"https://cdn.ckeditor.com/ckeditor5/43.3.1/ckeditor5.css\">"
	html = strings.Replace(html, "</head>", ckeditorStyles+"</head>", 1)

	return html, nil
}

// RenderLabelHTML renders the label HTML for a given order, dish, and label index
func RenderLabelHTML(db *gorm.DB, orderID string, dishIndex int, labelIndex int, size string) (string, error) {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return "", err
	}

	// Find site and hub
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return "", err
	}

	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		return "", err
	}

	// Find bill config for label
	var billConfig models.BrandBillConfig
	if err := db.Where("brand_id = ? AND bill_type = ?", "66fa204bc13e40f109e020c7", "bill_for_label").
		Where("active = ?", true).
		First(&billConfig).Error; err != nil {
		return "", err
	}

	// Get order data mapping from the DataMapping field
	dataMapping := make(map[string]interface{})
	dataMappingBytes, err := json.Marshal(order.DataMapping.Data)
	if err == nil {
		_ = json.Unmarshal(dataMappingBytes, &dataMapping)
	}

	// Get dishes from data mapping
	dishes, ok := dataMapping["dishes"].([]interface{})
	if !ok || dishIndex >= len(dishes) {
		return "", fmt.Errorf("dish index out of range")
	}

	dish, ok := dishes[dishIndex].(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("invalid dish data")
	}

	// Calculate label index per total
	totalQuantityBefore := 0
	for i := 0; i < dishIndex; i++ {
		if i >= len(dishes) {
			break
		}

		d, ok := dishes[i].(map[string]interface{})
		if !ok {
			continue
		}

		quantity := getFloatValue(d, "quantity")
		totalQuantityBefore += int(quantity)
	}

	labelIndexPerTotal := totalQuantityBefore + labelIndex + 1

	// Calculate total quantity
	totalQuantity := 0
	for _, d := range dishes {
		dish, ok := d.(map[string]interface{})
		if !ok {
			continue
		}

		quantity := getFloatValue(dish, "quantity")
		totalQuantity += int(quantity)
	}

	// Get merchant info
	merchantInfo := map[string]map[string]string{
		"grab":         {"label": "Grab Food"},
		"grab_mart":    {"label": "Grab Mart"},
		"shopee":       {"label": "Shopee Food"},
		"shopee_ecom":  {"label": "Shopee Ecom"},
		"shopee_fresh": {"label": "Shopee Fresh"},
		"gojek":        {"label": "Gojek"},
		"be":           {"label": "Be"},
		"local":        {"label": "Tạo trên NexPos"},
		"he":           {"label": "Đại lý cá nhân"},
		"lazada":       {"label": "Lazada"},
		"tiktok":       {"label": "Tiktok"},
	}

	// Get order source label
	orderSourceLabel := order.Source
	if sourceInfo, ok := merchantInfo[order.Source]; ok {
		orderSourceLabel = sourceInfo["label"]
	}

	// Create replacement map
	replacement := map[string]string{
		"bill_name":           billConfig.Name,
		"long_order_id":       order.OrderID,
		"site_name":           site.Name,
		"hub_name":            hub.Name,
		"hub_phone":           hub.Phone,
		"order_source":        orderSourceLabel,
		"dish_name":           getStringValue(dish, "name"),
		"dish_description":    getStringValue(dish, "description"),
		"dish_quantity":       fmt.Sprintf("%.0f", getFloatValue(dish, "quantity")),
		"dish_price":          FormatCurrency(getFloatValue(dish, "price")),
		"dish_discount_price": FormatCurrency(getFloatValue(dish, "discount_price")),
		"dish_note":           getStringValue(dish, "note"),
		"label_index":         fmt.Sprintf("%d/%d", labelIndexPerTotal, totalQuantity),
	}

	// Add data from order.DataMapping
	if shortOrderID, ok := dataMapping["order_id"].(string); ok {
		replacement["short_order_id"] = shortOrderID
	}

	if note, ok := dataMapping["note"].(string); ok {
		replacement["order_note"] = note
	} else {
		replacement["order_note"] = ""
	}

	// Format dates
	if createdAt, ok := dataMapping["created_at"].(string); ok {
		createdAtFormatted, _ := time.Parse(time.RFC3339, createdAt)
		replacement["order_created_at"] = createdAtFormatted.Format("02/01/2006 15:04:05")
	} else {
		replacement["order_created_at"] = time.Now().Format("02/01/2006 15:04:05")
	}

	// Customer information
	if customerName, ok := dataMapping["customer_name"].(string); ok {
		replacement["customer_name"] = customerName
	}

	if customerAddress, ok := dataMapping["customer_address"].(string); ok {
		replacement["customer_address"] = customerAddress
	}

	// Order totals
	if total, ok := dataMapping["total"].(float64); ok {
		replacement["order_sub_total"] = FormatCurrency(total)
	}

	if totalDiscount, ok := dataMapping["total_discount"].(float64); ok {
		replacement["order_total_discount"] = FormatCurrency(totalDiscount)
	}

	if totalForBiz, ok := dataMapping["total_for_biz"].(float64); ok {
		replacement["order_total_paid"] = FormatCurrency(totalForBiz)
	}

	// Get the HTML content
	billHTML := billConfig.ContentHTML

	// Apply replacements to the bill HTML
	billHTML = StringReplacer(billHTML, replacement)

	// Process options if available
	options, ok := dish["options"].([]interface{})
	if ok && len(options) > 0 {
		// Find option template in the HTML
		optionTemplateStart := strings.Index(billHTML, "{option_item}")
		if optionTemplateStart >= 0 {
			// Find the element containing the option template
			elementStart := strings.LastIndex(billHTML[:optionTemplateStart], "<")
			if elementStart >= 0 {
				elementEnd := strings.Index(billHTML[optionTemplateStart:], ">")
				if elementEnd >= 0 {
					elementEnd += optionTemplateStart + 1 // Add length of ">"

					// Extract the option template element
					var optionTemplateElement string

					// Find the closing tag
					tagName := ""
					for i := elementStart + 1; i < optionTemplateStart; i++ {
						if billHTML[i] == ' ' || billHTML[i] == '>' {
							tagName = billHTML[elementStart+1 : i]
							break
						}
					}

					if tagName != "" {
						closingTag := fmt.Sprintf("</%s>", tagName)
						closingTagPos := strings.Index(billHTML[elementEnd:], closingTag)
						if closingTagPos >= 0 {
							closingTagPos += elementEnd
							optionTemplateElement = billHTML[elementStart : closingTagPos+len(closingTag)]

							// Group options by name
							groupedOptions := make(map[string][]map[string]interface{})

							for _, optGroup := range options {
								optionGroup, ok := optGroup.([]interface{})
								if !ok {
									continue
								}

								for _, opt := range optionGroup {
									option, ok := opt.(map[string]interface{})
									if !ok {
										continue
									}

									optionName := getStringValue(option, "option_name")
									if optionName == "" {
										continue
									}

									if _, ok := groupedOptions[optionName]; !ok {
										groupedOptions[optionName] = []map[string]interface{}{}
									}

									groupedOptions[optionName] = append(groupedOptions[optionName], option)
								}
							}

							// Generate HTML for options
							var optionsHTML strings.Builder

							for optionName, toppings := range groupedOptions {
								for _, topping := range toppings {
									// Create a copy of the template
									optionHTML := optionTemplateElement

									// Replace placeholders
									optionHTML = strings.ReplaceAll(optionHTML, "{option_name}", optionName)
									optionHTML = strings.ReplaceAll(optionHTML, "{option_item}", getStringValue(topping, "option_item"))

									quantity := getFloatValue(topping, "quantity")
									if quantity > 0 {
										optionHTML = strings.ReplaceAll(optionHTML, "{option_quantity}", fmt.Sprintf(" x%.0f", quantity))
									} else {
										optionHTML = strings.ReplaceAll(optionHTML, "{option_quantity}", "")
									}

									price := getFloatValue(topping, "price")
									if price > 0 {
										optionHTML = strings.ReplaceAll(optionHTML, "{option_price}", " - "+FormatCurrency(price))
									} else {
										optionHTML = strings.ReplaceAll(optionHTML, "{option_price}", "")
									}

									optionsHTML.WriteString(optionHTML)
								}
							}

							// Replace the option template with all options
							billHTML = billHTML[:elementStart] + optionsHTML.String() + billHTML[closingTagPos+len(closingTag):]
						}
					}
				}
			}
		}
	}

	// Add necessary styles and classes
	if size == "" {
		size = "590px"
	}

	billHTML = strings.Replace(
		billHTML,
		"<body>",
		fmt.Sprintf("<body class=\"ck-content\" style=\"width: %s; height: 240px; background-color: white;\">", size),
		1,
	)

	// Add CKEditor styles
	ckeditorStyles := "<link rel=\"stylesheet\" type=\"text/css\" href=\"https://cdn.ckeditor.com/ckeditor5/43.3.1/ckeditor5.css\">"
	billHTML = strings.Replace(billHTML, "</head>", ckeditorStyles+"</head>", 1)

	return billHTML, nil
}

// Helper function to get string value from map
func getStringValue(data map[string]interface{}, key string) string {
	if value, ok := data[key].(string); ok {
		return value
	}
	return ""
}

// Helper function to get float value from map
func getFloatValue(data map[string]interface{}, key string) float64 {
	switch v := data[key].(type) {
	case float64:
		return v
	case int:
		return float64(v)
	case int64:
		return float64(v)
	case string:
		f, _ := strconv.ParseFloat(v, 64)
		return f
	}
	return 0
}

// GenerateOrderBills generates bills for an order
func GenerateOrderBills(db *gorm.DB, orderID string, options BillOptions) (*models.Order, error) {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return nil, err
	}

	if options.GenerateBill {
		// Render bill HTML
		billHTML, err := RenderBillHTML(db, orderID, options.BillType)
		if err != nil {
			return &order, err
		}

		if billHTML != "" {
			// Convert HTML to image URL using external service
			billURL, err := convertHTMLToBillURL(orderID, billHTML)
			if err != nil {
				return &order, err
			}

			// Update order with bill URL
			field := MapBillToOrderField[options.BillType]
			if field != "" {
				updates := map[string]any{
					field: billURL,
				}
				if err := db.Model(&order).Updates(updates).Error; err != nil {
					return &order, err
				}

				// Refresh order
				if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
					return &order, err
				}
			}
		}
	}

	if options.GenerateLabel {
		// Get order data mapping from the DataMapping field
		dataMapping := make(map[string]interface{})
		dataMappingBytes, err := json.Marshal(order.DataMapping.Data)
		if err == nil {
			_ = json.Unmarshal(dataMappingBytes, &dataMapping)
		}

		dishes, ok := dataMapping["dishes"].([]interface{})
		if !ok || len(dishes) == 0 {
			// No dishes to generate labels for
			return &order, nil
		}

		// Generate labels for each dish based on quantity
		var labelURLs []string
		if order.LabelURLs.Data != nil {
			labelURLs = order.LabelURLs.Data
		}

		for dishIndex, d := range dishes {
			dish, ok := d.(map[string]interface{})
			if !ok {
				continue
			}

			quantity := getFloatValue(dish, "quantity")
			if quantity <= 0 {
				quantity = 1
			}

			// Generate a label for each quantity
			for labelIndex := 0; labelIndex < int(quantity); labelIndex++ {
				// Render label HTML
				labelHTML, err := RenderLabelHTML(db, orderID, dishIndex, labelIndex, "590px")
				if err != nil {
					fmt.Printf("Error rendering label HTML: %v\n", err)
					continue
				}

				if labelHTML != "" {
					// Convert HTML to image URL
					labelURL, err := convertHTMLToBillURL(orderID, labelHTML)
					if err != nil {
						fmt.Printf("Error converting label HTML to URL: %v\n", err)
						continue
					}

					labelURLs = append(labelURLs, labelURL)
				}
			}
		}

		// Update order with label URLs
		updates := map[string]any{
			"label_urls": models.JSONField[[]string]{Data: labelURLs},
		}
		if err := db.Model(&order).Updates(updates).Error; err != nil {
			return &order, err
		}

		// Refresh order
		if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
			return &order, err
		}
	}

	return &order, nil
}

// SendOrderBillToChannel sends bills to various channels (printers, Zalo, etc.)
func SendOrderBillToChannel(db *gorm.DB, orderID string, options ChannelOptions) error {
	// Find order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		return err
	}

	// Find site and hub
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		return err
	}

	var hub models.Hub
	if err := db.Where("id = ?", order.HubID).First(&hub).Error; err != nil {
		return err
	}

	// Get Zalo group
	zaloGroup := site.ZaloGroup
	if zaloGroup == "" {
		zaloGroup = hub.ZaloGroup
	}

	var currentBillURL string
	var labelURLs []string

	if options.Bill {
		field := MapBillToOrderField[options.BillTemplate]
		if field != "" {
			// Get bill URL from order
			switch field {
			case "bill_url":
				currentBillURL = order.BillURL
			case "bill_for_payment_url":
				currentBillURL = order.BillForPaymentURL
			case "bill_for_complete_url":
				currentBillURL = order.BillForCompleteURL
			case "bill_for_complete_app_url":
				currentBillURL = order.BillForCompleteAppURL
			case "bill_for_cancel_url":
				currentBillURL = order.BillForCancelURL
			}

			// If bill URL is empty, generate it
			if currentBillURL == "" {
				newOrder, err := GenerateOrderBills(db, orderID, BillOptions{
					BillType:     options.BillTemplate,
					GenerateBill: true,
				})
				if err != nil {
					return err
				}

				// Get the updated bill URL
				switch field {
				case "bill_url":
					currentBillURL = newOrder.BillURL
				case "bill_for_payment_url":
					currentBillURL = newOrder.BillForPaymentURL
				case "bill_for_complete_url":
					currentBillURL = newOrder.BillForCompleteURL
				case "bill_for_complete_app_url":
					currentBillURL = newOrder.BillForCompleteAppURL
				case "bill_for_cancel_url":
					currentBillURL = newOrder.BillForCancelURL
				}
			}
		}
	}

	if options.Labels {
		// Generate labels if needed
		if len(order.LabelURLs.Data) == 0 {
			// Generate labels
			newOrder, err := GenerateOrderBills(db, orderID, BillOptions{
				BillType:      "bill_for_label",
				GenerateLabel: true,
			})
			if err == nil && newOrder != nil {
				// Update order with new label URLs
				updates := map[string]any{
					"label_urls": newOrder.LabelURLs,
				}
				if err := db.Model(&order).Updates(updates).Error; err != nil {
					fmt.Printf("Error updating order with label URLs: %v\n", err)
				}

				// Get the updated label URLs
				labelURLs = newOrder.LabelURLs.Data
			}
		} else {
			labelURLs = order.LabelURLs.Data
		}
	}

	// Send to Zalo if enabled
	if options.Zalo && zaloGroup != "" {
		// Send message if provided
		if options.ZaloMessage != "" {
			err := SendZaloMessage(zaloGroup, options.ZaloMessage, options.UrgentMessage)
			if err != nil {
				fmt.Printf("Error sending Zalo message: %v\n", err)
			}
		}

		// Send bill image if available
		if currentBillURL != "" {
			err := SendZaloImage(zaloGroup, currentBillURL)
			if err != nil {
				fmt.Printf("Error sending bill to Zalo: %v\n", err)
			}
		}
	}

	// Create print queue entries
	if currentBillURL != "" {
		printQueue := models.PrintQueue{
			SiteID:          order.SiteID,
			HubID:           order.HubID,
			OrderID:         order.OrderID,
			OrderPrintCount: 1,
			Source:          order.Source,
			PrintType:       "kitchen",
			Status:          "created",
			Data:            []byte("{}"),
			FileURL:         currentBillURL,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		if err := db.Create(&printQueue).Error; err != nil {
			return err
		}
	}

	// Send message to print_order topic
	// Get Redis client from the SDK
	redisClient := redis.RedisClient
	if redisClient != nil {
		billTemplate := ""
		labelTemplate := ""

		if options.Bill {
			billTemplate = options.BillTemplate
		}

		if options.Labels {
			labelTemplate = options.LabelTemplate
		}

		// Send message to print_order topic
		message := fmt.Sprintf("Order %s is ready to print", orderID)

		data := map[string]any{
			"bill_template":  nil,
			"label_template": nil,
			"hub_code":       hub.Code,
			"order_id":       orderID,
			"site_id":        order.SiteID,
			"bill_url":       nil,
			"label_urls":     nil,
		}

		// Only include bill data if billTemplate is provided
		if billTemplate != "" {
			data["bill_template"] = billTemplate
			data["bill_url"] = currentBillURL
		}

		// Only include label data if labelTemplate is provided
		if labelTemplate != "" {
			data["label_template"] = labelTemplate
			data["label_urls"] = labelURLs
		}

		err := utils.SendMessageToTopic(redisClient, "print_order", message, data)

		if err != nil {
			fmt.Printf("Error sending print order message: %v\n", err)
		}
	}

	return nil
}

// Helper function to convert HTML to bill URL
func convertHTMLToBillURL(orderID string, html string) (string, error) {
	// Use the HTMLToImage function from the bill package
	return HTMLToImage(orderID, html)
}

// Helper function to parse string to int64
func parseInt64(s string) (int64, error) {
	var i int64
	_, err := fmt.Sscanf(s, "%d", &i)
	return i, err
}

// formatCurrencyWithSymbol formats a number as Vietnamese currency with symbol
func formatCurrencyWithSymbol(amount float64) string {
	// Format as Vietnamese currency (VND)
	return fmt.Sprintf("%s ₫", formatNumber(amount))
}

// formatNumber formats a number with thousands separators
func formatNumber(num float64) string {
	// Convert to integer if it's a whole number
	if num == float64(int(num)) {
		return formatWithCommas(int(num))
	}

	// Format with 2 decimal places
	return formatWithCommas(int(num)) + fmt.Sprintf(",%02d", int(num*100)%100)
}

// formatWithCommas adds thousands separators to an integer
func formatWithCommas(n int) string {
	in := strconv.Itoa(n)
	out := make([]byte, 0, len(in)+(len(in)-1)/3)

	// Process the number from right to left
	for i, c := range in {
		if i > 0 && (len(in)-i)%3 == 0 {
			out = append(out, '.')
		}
		out = append(out, byte(c))
	}

	return string(out)
}

// StringReplacer replaces placeholders in a string with values from a map
func StringReplacer(str string, obj map[string]string) string {
	// Replace {prefix|key} pattern
	prefixPattern := regexp.MustCompile(`\{([^}]+\|[^}]+)\}`)
	str = prefixPattern.ReplaceAllStringFunc(str, func(match string) string {
		// Extract content between curly braces
		content := match[1 : len(match)-1]
		parts := strings.Split(content, "|")
		if len(parts) != 2 {
			return match
		}

		prefix := parts[0]
		key := parts[1]

		if value, ok := obj[key]; ok {
			if value != "" {
				return prefix + value
			}
			return ""
		}
		return match
	})

	// Replace {key} pattern
	simplePattern := regexp.MustCompile(`\{([^}]+)\}`)
	str = simplePattern.ReplaceAllStringFunc(str, func(match string) string {
		// Extract key between curly braces
		key := match[1 : len(match)-1]

		if value, ok := obj[key]; ok {
			return value
		}
		return match
	})

	return str
}

// SendZaloMessage sends a message to a Zalo group
func SendZaloMessage(groupLink string, message string, urgentMessage bool) error {
	// Prepare urgency parameter
	urgency := ""
	if urgentMessage {
		urgency = "2" // Urgent message
	}

	// Create request data
	formData := url.Values{}
	formData.Set("shareLink", groupLink)
	formData.Set("message", message)
	formData.Set("urgency", urgency)

	// Send request to Zalo Mini App service
	resp, err := http.PostForm(
		"https://zalo-mini-app.nexpos.io/api/zalo_chat/group/send_message",
		formData,
	)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send Zalo message, status code: %d", resp.StatusCode)
	}

	return nil
}

// SendZaloImage sends an image to a Zalo group
func SendZaloImage(groupLink string, imageURL string) error {
	// Download the image
	resp, err := http.Get(imageURL)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Read the image data
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// Create a buffer to hold the form data
	var b bytes.Buffer
	w := multipart.NewWriter(&b)

	// Add the group link field
	err = w.WriteField("shareLink", groupLink)
	if err != nil {
		return err
	}

	// Add the image field
	fw, err := w.CreateFormFile("image", "bill.png")
	if err != nil {
		return err
	}
	_, err = fw.Write(imageData)
	if err != nil {
		return err
	}

	// Close the writer
	w.Close()

	// Create the request
	req, err := http.NewRequest("POST", "https://zalo-mini-app.nexpos.io/api/zalo_chat/group/send_image", &b)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", w.FormDataContentType())

	// Send the request
	client := &http.Client{}
	resp2, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp2.Body.Close()

	// Check response status
	if resp2.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send Zalo image, status code: %d", resp2.StatusCode)
	}

	return nil
}
