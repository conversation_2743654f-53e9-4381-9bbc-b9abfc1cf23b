package utils

import (
	"crypto/md5"
	"encoding/hex"
)

func ObjectToMD5(data any) string {
	hash := md5.Sum([]byte(StructToJSON(data).Raw))
	return hex.EncodeToString(hash[:])
}

// NameToID converts a name to an ID using MD5 hash (similar to Node.js helper.name_to_id)
func NameToID(name string) string {
	slugified := SlugifyText(name)
	hash := md5.Sum([]byte(slugified))
	return hex.EncodeToString(hash[:])
}
