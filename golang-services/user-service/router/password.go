// Package router handles password management functions
package router

import (
	"context"
	"fmt"
	"net/http"

	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/email"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// ChangePassword updates the password for a logged-in user
func ChangePassword(c *gin.Context) {
	// Get database connection and user from middleware
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Define request structure
	var request struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Verify old password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(request.OldPassword)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "wrong_password",
		})
		return
	}

	// Hash and update new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(request.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to hash password",
		})
		return
	}

	// Update user's password in database
	if err := db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Follow: https://nexdor-tech.atlassian.net/browse/NP-1719 for logout from another browsers
	token := c.GetHeader("x-access-token")
	if token != "" {
		middlewares.GetRedis(c).Set(context.Background(), "token_black_list:"+token, "true", 24*time.Hour)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// ForgotPassword initiates the password reset process
func ForgotPassword(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Define request structure
	var request struct {
		Email        string  `json:"email"`
		Phone        string  `json:"phone"`
		BrandID      uint    `json:"brand_id"`
		RedirectType *string `json:"redirect_type"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Determine username from email or phone
	username := request.Email
	if username == "" {
		username = request.Phone
	}

	// Find user in database
	var user models.User
	if err := db.Where("username = ?", username).First(&user).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "user_not_found",
		})
		return
	}

	// Generate OTP
	otp := "123456"
	if utils.GetEnv("USE_MERCHANT_APPS", "") == "true" {
		otp = utils.GenerateOTP(6)
	}

	// Set OTP expiry time
	otpExpiry := time.Now().Add(15 * time.Minute)
	userOTP := models.UserOTP{
		VerifyType: "forgot_password",
		UserUID:    username,
		OTP:        otp,
		ExpiredAt:  otpExpiry,
	}

	// Send verification via appropriate channel
	if utils.IsValidEmail(username) {
		// Send email
		go email.SendEmail(user.Email, user.Name, "forgot_password", map[string]any{
			"name": user.Name,
			"code": otp,
			"url": fmt.Sprintf("%s/forgot_password?code=%s&email=%s&type=forgot_password",
				utils.GetWebURLToVerifyAccount(request.RedirectType), otp, user.Email),
		})
	} else {
		// Send OTP via Zalo
		if err := sendOTP(db, request.BrandID, username, otp); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
		userOTP.ReceiveMethod = "zalo"
	}

	// Save OTP in database
	if err := db.Create(&userOTP).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// ChangePasswordByCode resets password using verification code
func ChangePasswordByCode(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Define request structure
	var request struct {
		Email       string `json:"email"`
		Phone       string `json:"phone"`
		Code        string `json:"code" binding:"required"`
		NewPassword string `json:"new_password" binding:"required"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Determine username from email or phone
	username := request.Email
	if username == "" {
		username = request.Phone
	}

	// Find user in database
	var user models.User
	if err := db.Where("username = ?", username).First(&user).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "user_not_found",
		})
		return
	}

	// Check for too many login failures
	if user.LoginFailCount > 3 {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "user_try_login_many_times",
		})
		return
	}

	// Verify OTP
	var userOTP models.UserOTP
	if err := db.Where("verify_type = ? AND user_uid = ? AND otp = ? AND expired_at > ?",
		"forgot_password", username, request.Code, time.Now()).First(&userOTP).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "otp_invalid_or_expired",
		})
		return
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(request.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to hash password",
		})
		return
	}

	// Update password and reset login failure count
	if err := db.Model(&user).Updates(map[string]any{
		"status":           "active",
		"password":         string(hashedPassword),
		"login_fail_count": 0,
	}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Generate auth data for response
	authData, err := getUserAuth(db, &user)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    authData,
	})
}
